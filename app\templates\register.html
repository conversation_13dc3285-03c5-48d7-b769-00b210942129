<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册 - 账单分析系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        :root {
            --primary-color: #4361ee;
            --primary-light: rgba(67, 97, 238, 0.1);
            --secondary-color: #3f37c9;
            --accent-color: #4cc9f0;
            --success-color: #4ade80;
            --danger-color: #f87171;
            --bg-color: #f8fafc;
            --card-bg: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #cbd5e1;
        }

        body {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .bg-decoration {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            z-index: -1;
        }

        .bg-decoration-1 {
            top: -100px;
            left: -100px;
            width: 300px;
            height: 300px;
        }

        .bg-decoration-2 {
            bottom: -150px;
            right: -150px;
            width: 400px;
            height: 400px;
        }

        .register-container {
            max-width: 450px;
            width: 100%;
            padding: 20px;
        }

        .register-card {
            background: var(--card-bg);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            position: relative;
        }

        .register-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            text-align: center;
            padding: 35px 30px 25px;
            position: relative;
        }

        .register-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            opacity: 0.2;
        }

        .register-icon {
            font-size: 2.8rem;
            margin-bottom: 12px;
            position: relative;
            z-index: 1;
        }

        .register-title {
            font-size: 1.7rem;
            font-weight: 700;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .register-subtitle {
            opacity: 0.9;
            font-size: 0.95rem;
            position: relative;
            z-index: 1;
        }

        .register-body {
            padding: 30px;
        }

        .form-floating {
            margin-bottom: 18px;
        }

        .form-control {
            border-radius: 12px;
            border: 1px solid var(--border-color);
            padding: 12px 16px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
        }

        .form-floating > label {
            color: var(--text-secondary);
        }

        .input-group {
            margin-bottom: 18px;
        }

        .btn-outline-secondary {
            border-color: var(--border-color);
            color: var(--text-secondary);
            background: #f8fafc;
            border-radius: 0 12px 12px 0;
            font-weight: 600;
            white-space: nowrap;
        }

        .btn-outline-secondary:hover {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 12px;
            font-weight: 600;
            padding: 12px 24px;
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(67, 97, 238, 0.3);
        }

        .btn-primary:disabled {
            background: linear-gradient(135deg, #94a3b8, #cbd5e1);
            transform: none;
            box-shadow: none;
        }

        .alert {
            border-radius: 12px;
            padding: 16px 20px;
            margin-bottom: 20px;
            border: none;
            font-size: 0.95rem;
        }

        .alert-danger {
            background-color: rgba(248, 113, 113, 0.15);
            border-left: 4px solid var(--danger-color);
            color: #b91c1c;
        }

        .alert-success {
            background-color: rgba(74, 222, 128, 0.15);
            border-left: 4px solid var(--success-color);
            color: #15803d;
        }

        .register-footer {
            padding: 20px 30px;
            background-color: #f8fafc;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }

        .register-footer a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .register-footer a:hover {
            color: var(--secondary-color);
            text-decoration: underline;
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            text-decoration: none;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .back-btn:hover {
            background: white;
            color: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .countdown {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        @media (max-width: 480px) {
            .register-container {
                padding: 15px;
            }
            
            .register-body {
                padding: 25px 20px;
            }
            
            .register-header {
                padding: 30px 20px 25px;
            }
        }
    </style>
</head>
<body>
    <!-- 背景装饰 -->
    <div class="bg-decoration bg-decoration-1"></div>
    <div class="bg-decoration bg-decoration-2"></div>

    <!-- 返回按钮 -->
    <a href="/" class="back-btn" title="返回首页">
        <i class="bi bi-arrow-left"></i>
    </a>

    <div class="register-container">
        <div class="register-card">
            <div class="register-header">
                <div class="register-icon">
                    <i class="bi bi-person-plus"></i>
                </div>
                <h1 class="register-title">用户注册</h1>
                <p class="register-subtitle">创建您的账户以开始使用</p>
            </div>
            
            <div class="register-body">
                <div class="alert alert-danger" id="errorAlert" style="display: none;"></div>
                <div class="alert alert-success" id="successAlert" style="display: none;"></div>

                <form id="registerForm">
                    <div class="form-floating">
                        <input type="text" class="form-control" id="username" placeholder="用户名" required>
                        <label for="username">
                            <i class="bi bi-person me-2"></i>用户名
                        </label>
                    </div>

                    <div class="form-floating">
                        <input type="email" class="form-control" id="email" placeholder="邮箱地址" required>
                        <label for="email">
                            <i class="bi bi-envelope me-2"></i>邮箱地址
                        </label>
                    </div>

                    <div class="input-group">
                        <div class="form-floating flex-grow-1">
                            <input type="text" class="form-control" id="verification_code" placeholder="验证码" required>
                            <label for="verification_code">
                                <i class="bi bi-shield-check me-2"></i>验证码
                            </label>
                        </div>
                        <button type="button" class="btn btn-outline-secondary" id="sendCodeBtn">
                            获取验证码
                        </button>
                    </div>

                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" placeholder="密码" required>
                        <label for="password">
                            <i class="bi bi-lock me-2"></i>密码
                        </label>
                    </div>

                    <div class="form-floating">
                        <input type="password" class="form-control" id="confirm_password" placeholder="确认密码" required>
                        <label for="confirm_password">
                            <i class="bi bi-lock-fill me-2"></i>确认密码
                        </label>
                    </div>

                    <button type="submit" class="btn btn-primary" id="registerBtn">
                        <span class="btn-text">
                            <i class="bi bi-person-plus me-2"></i>注册账户
                        </span>
                        <div class="loading-spinner"></div>
                    </button>
                </form>
            </div>

            <div class="register-footer">
                <p class="mb-0">
                    已有账户？
                    <a href="/login">立即登录</a>
                </p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const registerForm = document.getElementById('registerForm');
            const registerBtn = document.getElementById('registerBtn');
            const sendCodeBtn = document.getElementById('sendCodeBtn');
            const errorAlert = document.getElementById('errorAlert');
            const successAlert = document.getElementById('successAlert');
            const loadingSpinner = registerBtn.querySelector('.loading-spinner');
            const btnText = registerBtn.querySelector('.btn-text');

            let countdown = 0;
            let countdownInterval = null;

            // 发送验证码
            sendCodeBtn.addEventListener('click', function() {
                const email = document.getElementById('email').value.trim();

                if (!email) {
                    showError('请先输入邮箱地址');
                    return;
                }

                if (!isValidEmail(email)) {
                    showError('请输入有效的邮箱地址');
                    return;
                }

                sendCodeBtn.disabled = true;
                const originalText = sendCodeBtn.textContent;
                sendCodeBtn.textContent = '发送中...';

                fetch('/send_verification_code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email: email })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        showError(data.error);
                        sendCodeBtn.disabled = false;
                        sendCodeBtn.textContent = originalText;
                    } else {
                        showSuccess(data.message);
                        startCountdown();
                    }
                })
                .catch(error => {
                    showError('发送验证码失败，请重试');
                    sendCodeBtn.disabled = false;
                    sendCodeBtn.textContent = originalText;
                    console.error('发送验证码错误:', error);
                });
            });

            // 注册表单提交
            registerForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const username = document.getElementById('username').value.trim();
                const email = document.getElementById('email').value.trim();
                const verificationCode = document.getElementById('verification_code').value.trim();
                const password = document.getElementById('password').value;
                const confirmPassword = document.getElementById('confirm_password').value;

                if (!username || !email || !verificationCode || !password || !confirmPassword) {
                    showError('请填写所有字段');
                    return;
                }

                if (!isValidEmail(email)) {
                    showError('请输入有效的邮箱地址');
                    return;
                }

                if (password.length < 6) {
                    showError('密码长度至少为6位');
                    return;
                }

                if (password !== confirmPassword) {
                    showError('两次输入的密码不一致');
                    return;
                }

                // 显示加载状态
                setLoading(true);
                hideAlerts();

                // 发送注册请求
                fetch('/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        email: email,
                        verification_code: verificationCode,
                        password: password
                    })
                })
                .then(response => response.json())
                .then(data => {
                    setLoading(false);
                    
                    if (data.error) {
                        showError(data.error);
                    } else {
                        showSuccess(data.message + '，正在跳转到登录页面...');
                        setTimeout(() => {
                            window.location.href = '/login';
                        }, 2000);
                    }
                })
                .catch(error => {
                    setLoading(false);
                    showError('注册失败，请重试');
                    console.error('注册错误:', error);
                });
            });

            function startCountdown() {
                countdown = 60;
                updateCountdownButton();
                
                countdownInterval = setInterval(() => {
                    countdown--;
                    if (countdown <= 0) {
                        clearInterval(countdownInterval);
                        sendCodeBtn.disabled = false;
                        sendCodeBtn.textContent = '获取验证码';
                        sendCodeBtn.classList.remove('countdown');
                    } else {
                        updateCountdownButton();
                    }
                }, 1000);
            }

            function updateCountdownButton() {
                sendCodeBtn.textContent = `${countdown}秒后重试`;
                sendCodeBtn.classList.add('countdown');
            }

            function isValidEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }

            function setLoading(loading) {
                registerBtn.disabled = loading;
                if (loading) {
                    btnText.style.display = 'none';
                    loadingSpinner.style.display = 'inline-block';
                } else {
                    btnText.style.display = 'inline-block';
                    loadingSpinner.style.display = 'none';
                }
            }

            function showError(message) {
                hideAlerts();
                errorAlert.textContent = message;
                errorAlert.style.display = 'block';
            }

            function showSuccess(message) {
                hideAlerts();
                successAlert.textContent = message;
                successAlert.style.display = 'block';
            }

            function hideAlerts() {
                errorAlert.style.display = 'none';
                successAlert.style.display = 'none';
            }
        });
    </script>
</body>
</html>