// 侧边栏导航功能增强
document.addEventListener('DOMContentLoaded', function() {
    // 添加平滑滚动效果
    function smoothScrollToTop() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    }

    // 为所有导航链接添加平滑滚动
    const navLinks = document.querySelectorAll('.nav-link[data-target]');
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            setTimeout(smoothScrollToTop, 100);
        });
    });

    // 添加键盘导航支持
    document.addEventListener('keydown', function(e) {
        // ESC键关闭侧边栏
        if (e.key === 'Escape') {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            const sidebarToggle = document.getElementById('sidebarToggle');
            
            if (!sidebar.classList.contains('collapsed')) {
                sidebar.classList.add('collapsed');
                mainContent.classList.add('expanded');
                sidebarToggle.classList.remove('moved');
            }
        }
    });

    // 添加触摸手势支持（移动设备）
    let touchStartX = 0;
    let touchEndX = 0;

    document.addEventListener('touchstart', function(e) {
        touchStartX = e.changedTouches[0].screenX;
    });

    document.addEventListener('touchend', function(e) {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipe();
    });

    function handleSwipe() {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');
        const sidebarToggle = document.getElementById('sidebarToggle');
        
        // 向右滑动打开侧边栏
        if (touchEndX > touchStartX + 50 && touchStartX < 50) {
            sidebar.classList.remove('collapsed');
            mainContent.classList.remove('expanded');
            sidebarToggle.classList.add('moved');
        }
        
        // 向左滑动关闭侧边栏
        if (touchStartX > touchEndX + 50 && touchStartX > window.innerWidth - 280) {
            sidebar.classList.add('collapsed');
            mainContent.classList.add('expanded');
            sidebarToggle.classList.remove('moved');
        }
    }

    // 添加活动状态持久化
    function saveActiveState(target) {
        localStorage.setItem('activeNavItem', target);
    }

    function loadActiveState() {
        const savedTarget = localStorage.getItem('activeNavItem');
        if (savedTarget) {
            const savedLink = document.querySelector(`[data-target="${savedTarget}"]`);
            const savedSection = document.getElementById(savedTarget);
            
            if (savedLink && savedSection) {
                // 移除所有活动状态
                document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                document.querySelectorAll('.content-section').forEach(s => s.classList.remove('active'));
                
                // 设置保存的活动状态
                savedLink.classList.add('active');
                savedSection.classList.add('active');
                
                // 更新面包屑
                const currentPageTitle = document.getElementById('currentPageTitle');
                if (currentPageTitle) {
                    currentPageTitle.textContent = savedLink.textContent.trim();
                }
            }
        }
    }

    // 为导航链接添加状态保存
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            const target = this.getAttribute('data-target');
            if (target && !this.getAttribute('data-toggle')) {
                saveActiveState(target);
            }
        });
    });

    // 页面加载时恢复活动状态
    loadActiveState();

    // 添加加载动画
    function showLoadingSpinner(sectionId) {
        const section = document.getElementById(sectionId);
        if (section) {
            const spinner = document.createElement('div');
            spinner.className = 'loading-spinner';
            spinner.innerHTML = '<div class="spinner"></div>';
            section.appendChild(spinner);
            
            // 模拟加载时间
            setTimeout(() => {
                spinner.remove();
            }, 500);
        }
    }

    // 为内容切换添加加载效果
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            const target = this.getAttribute('data-target');
            if (target && !this.getAttribute('data-toggle')) {
                showLoadingSpinner(target);
            }
        });
    });

    // 添加工具提示功能
    function initTooltips() {
        const navItems = document.querySelectorAll('.nav-link');
        navItems.forEach(item => {
            item.setAttribute('title', item.textContent.trim());
        });
    }

    initTooltips();

    // 添加搜索功能（可选）
    function addSearchFunctionality() {
        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.placeholder = '搜索功能...';
        searchInput.className = 'form-control form-control-sm mb-3';
        searchInput.style.margin = '10px 20px';
        searchInput.style.width = 'calc(100% - 40px)';
        
        const sidebarNav = document.querySelector('.sidebar-nav');
        if (sidebarNav) {
            sidebarNav.insertBefore(searchInput, sidebarNav.firstChild);
        }
        
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const navItems = document.querySelectorAll('.nav-link');
            
            navItems.forEach(item => {
                const text = item.textContent.toLowerCase();
                const parent = item.closest('.nav-section');
                
                if (text.includes(searchTerm) || searchTerm === '') {
                    parent.style.display = 'block';
                } else {
                    parent.style.display = 'none';
                }
            });
        });
    }

    // 可选：启用搜索功能
    // addSearchFunctionality();
});
