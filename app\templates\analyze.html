<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账单分析 - 结果</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/sidebar.css') }}">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
    <style>
        body {
            background-color: #f8f9fa;
            font-family: "Microsoft YaHei", sans-serif;
            padding-top: 80px; /* 为固定导航栏留出空间 */
        }
        
        /* 固定水平导航栏样式 */
        .horizontal-nav {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1050;
            height: 70px;
        }
        
        .horizontal-nav .navbar-brand {
            color: white !important;
            font-weight: 800; /* 从bold改为更粗的800 */
            font-size: 1.6rem; /* 从1.4rem增加到1.6rem */
        }
        
        /* 水平导航栏中的侧边栏切换按钮样式 */
        .sidebar-toggle-nav {
            background: rgba(255,255,255,0.2);
            border: none;
            border-radius: 8px;
            color: white;
            padding: 8px 12px;
            transition: all 0.3s ease;
            font-size: 1.2rem;
        }
        
        .sidebar-toggle-nav:hover {
            background: rgba(255,255,255,0.3);
            color: white;
            transform: translateY(-1px);
        }
        
        .sidebar-toggle-nav:focus {
            outline: none;
            box-shadow: 0 0 0 2px rgba(255,255,255,0.3);
        }
        
        /* 调整侧边栏位置以适应水平导航栏 */
        .sidebar {
            top: 70px;
            height: calc(100vh - 70px);
            width: 220px !important; /* 确保宽度被覆盖 */
        }
        
        .sidebar.collapsed {
            transform: translateX(-220px) !important; /* 确保折叠位置正确 */
        }
        
        .main-content {
            min-height: calc(100vh - 70px);
            margin-left: 220px !important; /* 确保主内容区域左边距正确 */
        }
        
        .main-content.expanded {
            margin-left: 0 !important; /* 展开时移除左边距 */
        }
        .container {
            padding-top: 30px;
            max-width: none;
            padding-left: 15px;
            padding-right: 15px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 6px 10px rgba(0,0,0,0.08);
            margin-bottom: 20px;
            border: none;
        }
        .card-header {
            background-color: #fff;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            padding: 15px 20px;
            border-radius: 15px 15px 0 0 !important;
        }
        .card-body {
            padding: 20px;
        }
        .summary-item {
            text-align: center;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
        }
        .summary-item h3 {
            margin: 10px 0;
            font-size: 24px;
        }
        .summary-item p {
            font-size: 14px;
            margin: 0;
            color: #6c757d;
        }
        .income {
            background-color: rgba(25, 135, 84, 0.1);
            color: #198754;
        }
        .expense {
            background-color: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }
        .balance {
            background-color: rgba(13, 110, 253, 0.1);
            color: #0d6efd;
        }
        .chart-container {
            height: 300px;
            margin-bottom: 20px;
        }
        .table-container {
            overflow-x: auto;
        }
        .table {
            font-size: 14px;
        }
        .nav-pills .nav-link.active {
            background-color: #0d6efd;
        }
        .nav-pills .nav-link {
            color: #6c757d;
        }
        .search-container {
            margin-bottom: 20px;
        }
        .pagination-container {
            display: flex;
            justify-content: center;
            margin-top: 15px;
        }
        .back-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
        /* 排序切换按钮样式 */
        #sortToggleBtn {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
            border-radius: 0.2rem;
            transition: all 0.2s ease-in-out;
            min-width: 70px;
        }
        #sortToggleBtn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            background-color: #6c757d;
            border-color: #6c757d;
            color: white;
        }
        #sortToggleBtn i {
            margin-right: 4px;
        }
        .card-header .d-flex.gap-2 {
            gap: 0.5rem !important;
        }
        /* 排行榜卡片体样式优化 */
        #rankingChart {
            margin-top: -10px;
        }
        .card-body {
            padding-top: 1rem;
        }

        /* 用户头像下拉菜单样式 */
        .user-dropdown {
            position: relative;
            margin-left: auto;
        }

        .user-info-trigger {
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .user-info-trigger:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid rgba(255, 255, 255, 0.3);
            margin-right: 8px;
        }

        .user-name {
            color: white;
            font-weight: 600;
            font-size: 0.95rem;
            margin-right: 6px;
        }

        .dropdown-arrow {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.8rem;
            transition: transform 0.3s ease;
        }

        .user-info-trigger.active .dropdown-arrow {
            transform: rotate(180deg);
        }

        .user-dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            min-width: 280px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            z-index: 2000;
            margin-top: 8px;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .user-dropdown-menu.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-header {
            padding: 20px;
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 12px 12px 0 0;
        }

        .dropdown-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #007bff;
            margin-right: 15px;
        }

        .dropdown-user-info {
            flex: 1;
        }

        .dropdown-username {
            font-weight: 700;
            font-size: 1.1rem;
            color: #2c3e50;
            margin-bottom: 2px;
        }

        .dropdown-email {
            font-size: 0.85rem;
            color: #6c757d;
        }

        .dropdown-divider {
            height: 1px;
            background: #e9ecef;
            margin: 8px 0;
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: #495057;
            text-decoration: none;
            transition: all 0.2s ease;
            border-radius: 0;
        }

        .dropdown-item:hover {
            background: #f8f9fa;
            color: #007bff;
            text-decoration: none;
        }

        .dropdown-item i {
            font-size: 1.1rem;
            margin-right: 12px;
            width: 20px;
            text-align: center;
        }

        .dropdown-item:last-child {
            border-radius: 0 0 12px 12px;
        }

        .dropdown-item:last-child:hover {
            color: #dc3545;
            background: rgba(220, 53, 69, 0.1);
        }

        /* 模态框样式 */
        .custom-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            z-index: 3000;
            display: none;
            align-items: center;
            justify-content: center;
        }

        .modal-content-custom {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .modal-header-custom {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 25px 30px 20px;
            border-radius: 20px 20px 0 0;
            position: relative;
        }

        .modal-close-custom {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .modal-close-custom:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .modal-title-custom {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0;
            display: flex;
            align-items: center;
        }

        .modal-title-custom i {
            font-size: 1.8rem;
            margin-right: 12px;
        }

        .modal-body-custom {
            padding: 30px;
        }

        .profile-upload-area {
            text-align: center;
            margin-bottom: 25px;
        }

        .current-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid #007bff;
            margin-bottom: 15px;
            display: block;
            margin-left: auto;
            margin-right: auto;
        }

        .upload-btn {
            background: #007bff;
            color: white;
            border: none;
            border-radius: 25px;
            padding: 8px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .upload-btn:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .form-group-custom {
            margin-bottom: 20px;
        }

        .form-label-custom {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
            display: block;
        }

        .form-control-custom {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #ced4da;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control-custom:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            outline: none;
        }

        .btn-primary-custom {
            background: linear-gradient(135deg, #007bff, #0056b3);
            border: none;
            border-radius: 10px;
            font-weight: 600;
            padding: 12px 24px;
            width: 100%;
            transition: all 0.3s ease;
            color: white;
            cursor: pointer;
        }

        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
        }

        .alert-custom {
            border-radius: 10px;
            padding: 12px 16px;
            margin-bottom: 20px;
            border: none;
            font-size: 0.9rem;
        }

        .alert-success-custom {
            background-color: rgba(40, 167, 69, 0.15);
            border-left: 4px solid #28a745;
            color: #155724;
        }

        .alert-danger-custom {
            background-color: rgba(220, 53, 69, 0.15);
            border-left: 4px solid #dc3545;
            color: #721c24;
        }
    </style>
</head>
<body>
    <!-- 固定水平导航栏 -->
    <nav class="navbar navbar-expand-lg horizontal-nav">
        <div class="container-fluid">
            <div class="d-flex align-items-center">
                <a class="navbar-brand d-flex align-items-center me-3" href="/">
                    <i class="bi bi-graph-up me-2"></i>
                    账单分析系统
                </a>
                <!-- 侧边栏切换按钮紧跟在品牌标识右边 -->
                <button class="sidebar-toggle-nav" id="sidebarToggle">
                    <i class="bi bi-list"></i>
                </button>
            </div>
            
            <!-- 用户头像下拉菜单 -->
            <div class="user-dropdown">
                <div class="user-info-trigger" id="userDropdownTrigger">
                    <img src="{{ url_for('get_avatar', user_id=session.get('user_id', 'default')) }}" 
                         alt="用户头像" class="user-avatar" id="userAvatar">
                    <span class="user-name">{{ session.get('username', '用户') }}</span>
                    <i class="bi bi-chevron-down dropdown-arrow"></i>
                </div>
                
                <div class="user-dropdown-menu" id="userDropdownMenu">
                    <div class="dropdown-header">
                        <img src="{{ url_for('get_avatar', user_id=session.get('user_id', 'default')) }}" 
                             alt="用户头像" class="dropdown-avatar">
                        <div class="dropdown-user-info">
                            <div class="dropdown-username">{{ session.get('username', '用户') }}</div>
                            <div class="dropdown-email">{{ session.get('email', '') }}</div>
                        </div>
                    </div>
                    
                    <div class="dropdown-divider"></div>
                    
                    <a href="#" class="dropdown-item" onclick="showUserProfile()">
                        <i class="bi bi-person-circle"></i>
                        <span>个人中心</span>
                    </a>
                    
                    <a href="#" class="dropdown-item" onclick="showUserSecurity()">
                        <i class="bi bi-shield-lock"></i>
                        <span>账户安全</span>
                    </a>
                    
                    <a href="#" class="dropdown-item" onclick="showUserSettings()">
                        <i class="bi bi-gear"></i>
                        <span>设置</span>
                    </a>
                    
                    <div class="dropdown-divider"></div>
                    
                    <a href="/logout" class="dropdown-item">
                        <i class="bi bi-box-arrow-right"></i>
                        <span>退出登录</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 左侧导航栏 -->
    <div class="sidebar" id="sidebar">
        <nav class="sidebar-nav">
            <div class="nav-section">
                <a href="#" class="nav-link active" data-target="overview">
                    <i class="bi bi-speedometer2"></i>
                    数据概览
                </a>
            </div>
            <div class="nav-section">
                <a href="#" class="nav-link" data-target="charts" data-toggle="submenu">
                    <i class="bi bi-bar-chart"></i>
                    图表分析
                    <i class="bi bi-chevron-right ms-auto nav-toggle-icon"></i>
                </a>
                <div class="nav-submenu" id="charts-submenu">
                    <a href="#" class="nav-link" data-target="category-analysis">
                        <i class="bi bi-pie-chart"></i>
                        分类分析
                    </a>
                    <a href="#" class="nav-link" data-target="trend-analysis">
                        <i class="bi bi-graph-up"></i>
                        趋势分析
                    </a>
                    <a href="#" class="nav-link" data-target="account-analysis">
                        <i class="bi bi-wallet2"></i>
                        账户分析
                    </a>
                    <a href="#" class="nav-link" data-target="time-analysis">
                        <i class="bi bi-clock"></i>
                        时间分析
                    </a>
                    <a href="#" class="nav-link" data-target="comprehensive-analysis">
                        <i class="bi bi-diagram-3"></i>
                        综合分析
                    </a>
                </div>
            </div>
            <div class="nav-section">
                <a href="#" class="nav-link" data-target="ranking">
                    <i class="bi bi-trophy"></i>
                    排行榜
                </a>
            </div>
            <div class="nav-section">
                <a href="#" class="nav-link" data-target="xianyuearnings">
                    <i class="bi bi-shop"></i>
                    闲鱼收益
                </a>
            </div>
            <div class="nav-section">
                <a href="#" class="nav-link" data-target="details">
                    <i class="bi bi-table"></i>
                    明细数据
                </a>
            </div>
            <div class="nav-section">
                <a href="#" class="nav-link" data-target="upload-records">
                    <i class="bi bi-file-earmark-arrow-up"></i>
                    上传记录
                </a>
            </div>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content" id="mainContent">
        <div class="container">

            <!-- 数据概览区域 -->
            <div class="content-section active" id="overview">
                <h2 class="page-title">数据概览</h2>
                <!-- 总览卡片 -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="summary-item income">
                            <i class="bi bi-wallet2 fs-1"></i>
                            <h3 id="totalIncome">¥0.00</h3>
                            <p>总收入</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="summary-item expense">
                            <i class="bi bi-cash-stack fs-1"></i>
                            <h3 id="totalExpense">¥0.00</h3>
                            <p>总支出</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="summary-item balance">
                            <i class="bi bi-piggy-bank fs-1"></i>
                            <h3 id="balance">¥0.00</h3>
                            <p>结余</p>
                        </div>
                    </div>
                </div>

                <!-- 统计指标卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="summary-item" style="background-color: rgba(111, 66, 193, 0.1); color: #6f42c1;">
                            <i class="bi bi-graph-up fs-1"></i>
                            <h3 id="avgDailyIncome">¥0.00</h3>
                            <p>日均收入</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="summary-item" style="background-color: rgba(253, 126, 20, 0.1); color: #fd7e14;">
                            <i class="bi bi-graph-down fs-1"></i>
                            <h3 id="avgDailyExpense">¥0.00</h3>
                            <p>日均支出</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="summary-item" style="background-color: rgba(32, 201, 151, 0.1); color: #20c997;">
                            <i class="bi bi-calendar-range fs-1"></i>
                            <h3 id="totalDays">0</h3>
                            <p>记录天数</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="summary-item" style="background-color: rgba(108, 117, 125, 0.1); color: #6c757d;">
                            <i class="bi bi-receipt fs-1"></i>
                            <h3 id="totalRecords">0</h3>
                            <p>交易笔数</p>
                        </div>
                    </div>
                </div>

                <!-- 数据分析卡片 -->
                <div class="row">
                    <!-- 收支比例分析 -->
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-pie-chart-fill me-2"></i>收支比例
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="progress flex-grow-1 me-3" style="height: 20px;">
                                        <div class="progress-bar bg-success" role="progressbar" id="incomeProgress" style="width: 50%" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
                                        <div class="progress-bar bg-danger" role="progressbar" id="expenseProgress" style="width: 50%" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                                <div class="row text-center">
                                    <div class="col-6">
                                        <small class="text-success fw-bold">收入占比</small>
                                        <div id="incomeRatio" class="h6 text-success">50%</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-danger fw-bold">支出占比</small>
                                        <div id="expenseRatio" class="h6 text-danger">50%</div>
                                    </div>
                                </div>
                                <hr class="my-3">
                                <div class="text-center">
                                    <small class="text-muted">储蓄率</small>
                                    <div id="savingsRate" class="h5 fw-bold" style="color: #0d6efd;">0%</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 最大单笔交易 -->
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-award-fill me-2"></i>交易极值
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="text-success fw-bold">最大收入</span>
                                        <span id="maxIncome" class="text-success h6 mb-0">¥0.00</span>
                                    </div>
                                    <small id="maxIncomeDate" class="text-muted">-</small>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="text-danger fw-bold">最大支出</span>
                                        <span id="maxExpense" class="text-danger h6 mb-0">¥0.00</span>
                                    </div>
                                    <small id="maxExpenseDate" class="text-muted">-</small>
                                </div>
                                <hr class="my-3">
                                <div class="text-center">
                                    <small class="text-muted">平均单笔金额</small>
                                    <div id="avgAmount" class="h6 fw-bold" style="color: #6f42c1;">¥0.00</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 活跃度分析 -->
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-activity me-2"></i>活跃度分析
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="fw-bold">最活跃日期</span>
                                        <span id="mostActiveDate" class="h6 mb-0" style="color: #20c997;">-</span>
                                    </div>
                                    <small id="mostActiveDateCount" class="text-muted">0笔交易</small>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="fw-bold">连续记录</span>
                                        <span id="consecutiveDays" class="h6 mb-0" style="color: #fd7e14;">0天</span>
                                    </div>
                                    <small class="text-muted">最长连续记录天数</small>
                                </div>
                                <hr class="my-3">
                                <div class="text-center">
                                    <small class="text-muted">日均交易频次</small>
                                    <div id="avgDailyTransactions" class="h6 fw-bold" style="color: #6c757d;">0笔</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 月度对比分析 -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-calendar3 me-2"></i>月度数据对比
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row" id="monthlyComparison">
                                    <!-- 动态填充月度对比数据 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据分析图表 -->
                <div class="row mt-4">
                    <!-- 收支趋势折线图 -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-graph-up me-2"></i>收支趋势分析
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="overviewTrendChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 月度收支对比柱状图 -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-bar-chart me-2"></i>月度收支对比
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="overviewMonthlyChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 详细数据分析 -->
                <div class="row mt-4">
                    <!-- 分类支出柱状图 -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-pie-chart me-2"></i>主要支出分类
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="overviewCategoryChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 日均收支对比雷达图 -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-diagram-3 me-2"></i>日均收支分析
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="overviewDailyAverageChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 累计趋势和账户分析 -->
                <div class="row mt-4">
                    <!-- 累计金额变化折线图 -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-activity me-2"></i>累計资金变化
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="overviewCumulativeChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 账户资金分布饼图 -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-wallet2 me-2"></i>账户资金分布
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="overviewAccountChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 分类分析区域 -->
            <div class="content-section" id="category-analysis">
                <h2 class="page-title">分类分析</h2>
                <div class="row">
                    <!-- 分类占比饼图 -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">支出分类占比</h5>
                            </div>
                            <div class="card-body">
                                <div id="categoryPieChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 收支类型占比饼图 -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">收支类型占比</h5>
                            </div>
                            <div class="card-body">
                                <div id="typePieChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 趋势分析区域 -->
            <div class="content-section" id="trend-analysis">
                <h2 class="page-title">趋势分析</h2>
                <div class="row">
                    <!-- 趋势图 -->
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">收支趋势</h5>
                            </div>
                            <div class="card-body">
                                <div id="trendChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- 月度收支对比柱状图 -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">月度收支对比</h5>
                            </div>
                            <div class="card-body">
                                <div id="monthlyChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 收支累计图 -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">收支累计趋势</h5>
                            </div>
                            <div class="card-body">
                                <div id="cumulativeChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 账户分析区域 -->
            <div class="content-section" id="account-analysis">
                <h2 class="page-title">账户分析</h2>
                <div class="row">
                    <!-- 账户分布饼图 -->
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">账户资金分布</h5>
                            </div>
                            <div class="card-body">
                                <div id="accountChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 时间分析区域 -->
            <div class="content-section" id="time-analysis">
                <h2 class="page-title">时间分析</h2>

                <div class="row">
                    <!-- 周收支热力图 -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">周收支活跃度</h5>
                            </div>
                            <div class="card-body">
                                <div id="weeklyHeatmap" class="chart-container"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 时段分析柱状图 -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">时段消费分析</h5>
                            </div>
                            <div class="card-body">
                                <div id="hourlyChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 综合分析区域 -->
            <div class="content-section" id="comprehensive-analysis">
                <h2 class="page-title">综合分析</h2>
                <div class="row">
                    <!-- 分类收支对比雷达图 -->
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">分类收支雷达图</h5>
                            </div>
                            <div class="card-body">
                                <div id="radarChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 排行榜区域 -->
            <div class="content-section" id="ranking">
                <h2 class="page-title">排行榜</h2>
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="btn-toolbar justify-content-center" role="toolbar">
                            <div class="btn-group" role="group" aria-label="切换排行榜类型">
                                <button type="button" class="btn btn-primary active px-4 py-2" id="expenseRankingBtn">
                                    <i class="bi bi-cash-stack me-1"></i>支出排行榜
                                </button>
                                <button type="button" class="btn btn-outline-primary px-4 py-2" id="incomeRankingBtn">
                                    <i class="bi bi-wallet2 me-1"></i>收入排行榜
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0" id="rankingTitle">支出排行榜</h5>
                                <div class="d-flex align-items-center gap-2">
                                    <!-- 排序切换按钮 -->
                                    <button type="button" class="btn btn-outline-secondary btn-sm" id="sortToggleBtn" title="点击切换排序方式">
                                        <i class="bi bi-sort-numeric-up" id="sortIcon"></i>
                                        <span id="sortText">升序</span>
                                    </button>
                                    <span class="badge bg-info" id="rankingCount">0项</span>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- 添加Loading指示器 -->
                                <div id="rankingLoading" class="text-center py-5">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <p class="mt-2">正在加载排行榜数据...</p>
                                </div>
                                
                                <!-- 图表容器 -->
                                <div id="rankingChart" class="chart-container" style="width: 100%; min-height: 600px;"></div>
                                
                                <!-- 错误信息容器 -->
                                <div id="rankingError" class="alert alert-danger" style="display: none;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 闲鱼收益区域 -->
            <div class="content-section" id="xianyuearnings">
                <h2 class="page-title">闲鱼收益分析</h2>
                
                <!-- 收益概览卡片 -->
                <div class="row mb-4">
                    <div class="col-md-2">
                        <div class="summary-item" style="background-color: rgba(220, 53, 69, 0.1); color: #dc3545;">
                            <i class="bi bi-gear fs-1"></i>
                            <h3 id="basicServiceFee">¥0.00</h3>
                            <p>基础软件服务费</p>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="summary-item" style="background-color: rgba(255, 193, 7, 0.1); color: #ffc107;">
                            <i class="bi bi-coin fs-1"></i>
                            <h3 id="smartServiceFee">¥0.00</h3>
                            <p>闲鱼币智能软件服务费</p>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="summary-item" style="background-color: rgba(255, 106, 0, 0.1); color: #ff6a00;">
                            <i class="bi bi-star fs-1"></i>
                            <h3 id="superBrightFee">¥0.00</h3>
                            <p>闲鱼超级擦亮</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="summary-item income">
                            <i class="bi bi-briefcase fs-1"></i>
                            <h3 id="businessIncome">¥0.00</h3>
                            <p>生意收入</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="summary-item balance">
                            <i class="bi bi-calculator fs-1"></i>
                            <h3 id="netEarnings">¥0.00</h3>
                            <p>净收益</p>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- 收益趋势图 -->
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">每日收益趋势</h5>
                            </div>
                            <div class="card-body">
                                <div id="xianyuEarningsChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <!-- 明细表格 -->
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">闲鱼收益明细</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-container">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th scope="col">日期</th>
                                                <th scope="col">基础软件服务费</th>
                                                <th scope="col">闲鱼币智能软件服务费</th>
                                                <th scope="col">闲鱼超级擦亮</th>
                                                <th scope="col">生意收入</th>
                                                <th scope="col">净收益</th>
                                            </tr>
                                        </thead>
                                        <tbody id="xianyuTableBody">
                                            <!-- 数据将通过JavaScript填充 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 明细数据区域 -->
            <div class="content-section" id="details">
                <h2 class="page-title">明细数据</h2>
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">账单明细</h5>
                    </div>
                    <div class="card-body">
                        <div class="search-container">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <input type="text" class="form-control" id="searchText" placeholder="搜索备注...">
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select" id="categoryFilter">
                                        <option value="">全部分类</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select" id="typeFilter">
                                        <option value="">全部收支类型</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-primary" id="searchBtn">搜索</button>
                                    <button class="btn btn-secondary" id="resetBtn">重置</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="table-container">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th scope="col">时间</th>
                                        <th scope="col">分类</th>
                                        <th scope="col">收支类型</th>
                                        <th scope="col">金额</th>
                                        <th scope="col">备注</th>
                                        <th scope="col">账户</th>
                                        <th scope="col">来源</th>
                                    </tr>
                                </thead>
                                <tbody id="dataTableBody">
                                    <!-- 数据将通过JavaScript填充 -->
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="pagination-container">
                            <nav aria-label="Page navigation">
                                <ul class="pagination" id="pagination">
                                    <!-- 分页将通过JavaScript填充 -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 个人中心区域 -->
            <div class="content-section" id="user-profile">
                <h2 class="page-title">个人中心</h2>
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-person-circle me-2"></i>个人信息
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-success" id="profileSuccessMessage" style="display: none;"></div>
                                <div class="alert alert-danger" id="profileErrorMessage" style="display: none;"></div>
                                
                                <div class="text-center mb-4">
                                    <img src="{{ url_for('get_avatar', user_id=session.get('user_id', 'default')) }}" 
                                         alt="用户头像" class="rounded-circle mb-3" id="profileAvatar"
                                         style="width: 120px; height: 120px; object-fit: cover; border: 4px solid #007bff;">
                                    <div>
                                        <input type="file" id="profileAvatarInput" accept="image/*" style="display: none;">
                                        <button class="btn btn-primary btn-sm" onclick="document.getElementById('profileAvatarInput').click()">
                                            <i class="bi bi-camera me-2"></i>更换头像
                                        </button>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">用户名</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="profileUsernameDisplay" 
                                                       value="{{ session.get('username', '') }}" readonly>
                                                <button class="btn btn-outline-secondary" type="button" id="editUsernameBtn">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button class="btn btn-success" type="button" id="saveUsernameBtn" style="display: none;">
                                                    <i class="bi bi-check"></i>
                                                </button>
                                                <button class="btn btn-secondary" type="button" id="cancelUsernameBtn" style="display: none;">
                                                    <i class="bi bi-x"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">邮箱地址</label>
                                            <input type="email" class="form-control" id="profileEmailDisplay" 
                                                   value="{{ session.get('email', '') }}" readonly>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">注册时间</label>
                                    <input type="text" class="form-control" id="profileCreateTimeDisplay" 
                                           value="加载中..." readonly>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 账户安全区域 -->
            <div class="content-section" id="user-security">
                <h2 class="page-title">账户安全</h2>
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="alert alert-success" id="securitySuccessMessage" style="display: none;"></div>
                        <div class="alert alert-danger" id="securityErrorMessage" style="display: none;"></div>
                        
                        <!-- 修改密码卡片 -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-lock me-2"></i>修改密码
                                </h5>
                            </div>
                            <div class="card-body">
                                <form id="securityChangePasswordForm">
                                    <div class="mb-3">
                                        <label class="form-label">邮箱验证码</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="securityPasswordVerificationCode" 
                                                   placeholder="请输入验证码" required>
                                            <button type="button" class="btn btn-outline-secondary" id="securitySendPasswordCodeBtn">
                                                获取验证码
                                            </button>
                                        </div>
                                        <small class="text-muted">验证码将发送到您的邮箱：{{ session.get('email', '') }}</small>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">新密码</label>
                                        <input type="password" class="form-control" id="securityNewPassword" 
                                               placeholder="请输入新密码（至少6位）" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">确认新密码</label>
                                        <input type="password" class="form-control" id="securityConfirmNewPassword" 
                                               placeholder="请再次输入新密码" required>
                                    </div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-lock me-2"></i>修改密码
                                    </button>
                                </form>
                            </div>
                        </div>
                        
                        <!-- 修改邮箱卡片 -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-envelope me-2"></i>修改邮箱
                                </h5>
                            </div>
                            <div class="card-body">
                                <form id="securityChangeEmailForm">
                                    <div class="mb-3">
                                        <label class="form-label">新邮箱地址</label>
                                        <input type="email" class="form-control" id="securityNewEmail" 
                                               placeholder="请输入新的邮箱地址" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">邮箱验证码</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="securityEmailVerificationCode" 
                                                   placeholder="请输入验证码" required>
                                            <button type="button" class="btn btn-outline-secondary" id="securitySendEmailCodeBtn">
                                                获取验证码
                                            </button>
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-envelope me-2"></i>修改邮箱
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 设置区域 -->
            <div class="content-section" id="user-settings">
                <h2 class="page-title">系统设置</h2>
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="alert alert-success" id="settingsSuccessMessage" style="display: none;"></div>
                        <div class="alert alert-danger" id="settingsErrorMessage" style="display: none;"></div>
                        
                        <!-- 数据管理卡片 -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-database me-2"></i>数据管理
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">导出数据</label>
                                    <p class="text-muted small mb-2">导出您的所有账单数据为CSV格式文件</p>
                                    <button class="btn btn-success" onclick="exportUserDataInPage()">
                                        <i class="bi bi-download me-2"></i>导出我的数据
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 危险操作卡片 -->
                        <div class="card mb-4">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-exclamation-triangle me-2"></i>危险操作
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">清空账单数据</label>
                                    <p class="text-muted small mb-2">此操作将永久删除您的所有账单数据，无法恢复</p>
                                    <button class="btn btn-warning" onclick="confirmClearDataInPage()">
                                        <i class="bi bi-trash me-2"></i>清空所有数据
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 账户操作卡片 -->
                        <div class="card">
                            <div class="card-header bg-danger text-white">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-person-x me-2"></i>账户操作
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">注销账户</label>
                                    <p class="text-muted small mb-2">此操作将永久删除您的账户和所有相关数据，无法恢复</p>
                                    <button class="btn btn-danger" onclick="confirmDeleteAccountInPage()">
                                        <i class="bi bi-person-x me-2"></i>注销账户
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 上传记录区域 -->
            <div class="content-section" id="upload-records">
                <h2 class="page-title">上传记录</h2>
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">文件上传历史</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success" id="recordSuccessMessage" style="display: none;"></div>
                        <div class="alert alert-danger" id="recordErrorMessage" style="display: none;"></div>
                        
                        <div class="table-container">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th scope="col">上传时间</th>
                                        <th scope="col">文件名</th>
                                        <th scope="col">文件大小</th>
                                        <th scope="col">记录数量</th>
                                        <th scope="col">状态</th>
                                        <th scope="col">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="uploadRecordsTableBody">
                                    <!-- 上传记录将通过JavaScript填充 -->
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="text-center" id="noRecordsMessage" style="display: none;">
                            <i class="bi bi-file-earmark-arrow-up fs-1 text-muted"></i>
                            <p class="text-muted mt-3">暂无上传记录</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <a href="/" class="btn btn-primary rounded-circle back-btn">
        <i class="bi bi-house-door"></i>
    </a>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 导航栏功能初始化
            initSidebar();

            function initSidebar() {
                const sidebar = document.getElementById('sidebar');
                const sidebarToggle = document.getElementById('sidebarToggle');
                const mainContent = document.getElementById('mainContent');
                const navLinks = document.querySelectorAll('.nav-link[data-target]');
                const contentSections = document.querySelectorAll('.content-section');

                // 侧边栏切换功能
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('collapsed');
                    mainContent.classList.toggle('expanded');
                });

                // 导航链接点击事件
                navLinks.forEach(link => {
                    link.addEventListener('click', function(e) {
                        e.preventDefault();

                        const target = this.getAttribute('data-target');
                        const hasSubmenu = this.getAttribute('data-toggle') === 'submenu';

                        if (hasSubmenu) {
                            // 处理子菜单展开/收起
                            const submenu = document.getElementById(target + '-submenu');
                            const toggleIcon = this.querySelector('.nav-toggle-icon');

                            if (submenu) {
                                submenu.classList.toggle('show');
                                toggleIcon.classList.toggle('rotated');
                            }
                        } else {
                            // 处理内容区域切换
                            // 移除所有活动状态
                            navLinks.forEach(l => l.classList.remove('active'));
                            contentSections.forEach(section => section.classList.remove('active'));

                            // 添加当前活动状态
                            this.classList.add('active');
                            const targetSection = document.getElementById(target);
                            if (targetSection) {
                                targetSection.classList.add('active');

                                // 初始化该区域的图表
                                initializeSectionCharts(target);

                                // 如果是移动设备，自动收起侧边栏
                                if (window.innerWidth <= 768) {
                                    sidebar.classList.add('collapsed');
                                    mainContent.classList.add('expanded');
                                }
                            }
                        }
                    });
                });

                // 响应式处理
                function handleResize() {
                    if (window.innerWidth <= 768) {
                        sidebar.classList.add('collapsed');
                        mainContent.classList.add('expanded');
                    } else {
                        sidebar.classList.remove('collapsed');
                        mainContent.classList.remove('expanded');
                    }
                }

                // 监听窗口大小变化
                window.addEventListener('resize', handleResize);
                window.addEventListener('resize', () => {
                    setTimeout(window.resizeAllCharts, 100);
                });

                // 初始化响应式状态
                handleResize();

                // 全局图表重新调整大小函数
                window.resizeAllCharts = function() {
                    const allChartContainers = document.querySelectorAll('[id$="Chart"], [id$="Heatmap"]');
                    allChartContainers.forEach(container => {
                        const chartInstance = echarts.getInstanceByDom(container);
                        if (chartInstance) {
                            chartInstance.resize();
                        }
                    });
                };
            }

            // 图表重新调整大小函数 - 移动到全局作用域
            function resizeChartsInSection(sectionId) {
                const section = document.getElementById(sectionId);
                if (!section) return;

                // 查找该区域内的所有图表容器
                const chartContainers = section.querySelectorAll('[id$="Chart"], [id$="Heatmap"]');
                chartContainers.forEach(container => {
                    const chartInstance = echarts.getInstanceByDom(container);
                    if (chartInstance) {
                        chartInstance.resize();
                    }
                });
            }
            // 存储图表数据，延迟初始化
            let chartData = {};

            // 获取汇总数据
            console.log('开始获取汇总数据...');
            fetch('/api/summary')
                .then(response => response.json())
                .then(data => {
                    console.log('汇总数据获取成功:', data);

                    // 更新摘要信息
                    document.getElementById('totalIncome').textContent = '¥' + data.total_income.toFixed(2);
                    document.getElementById('totalExpense').textContent = '¥' + data.total_expense.toFixed(2);
                    document.getElementById('balance').textContent = '¥' + data.balance.toFixed(2);

                    // 存储图表数据
                    chartData = data;
                    console.log('图表数据已存储，可以开始初始化图表');

                    // 计算并更新概览页面的详细统计
                    updateOverviewStatistics(data);

                    // 只初始化当前可见区域的图表（概览区域）
                    // 其他图表将在用户切换到相应区域时初始化
                })
                .catch(error => {
                    console.error('获取汇总数据出错:', error);
                });

            // 更新概览页面的统计数据
            function updateOverviewStatistics(summaryData) {
                // 需要明细数据来计算更详细的统计
                if (allData && allData.length > 0) {
                    calculateDetailedStatistics(allData, summaryData);
                } else {
                    // 如果明细数据还没加载，延迟计算
                    setTimeout(() => {
                        if (allData && allData.length > 0) {
                            calculateDetailedStatistics(allData, summaryData);
                        }
                    }, 1000);
                }
            }

            // 计算详细统计数据
            function calculateDetailedStatistics(data, summaryData) {
                console.log('开始计算详细统计数据...');
                
                // 过滤有效数据
                const validData = data.filter(item => 
                    item.记录时间 && item.金额 && !isNaN(parseFloat(item.金额))
                );

                if (validData.length === 0) {
                    console.log('没有有效数据用于统计');
                    return;
                }

                // 按日期分组
                const dateGroups = {};
                validData.forEach(item => {
                    const date = new Date(item.记录时间).toISOString().split('T')[0];
                    if (!dateGroups[date]) {
                        dateGroups[date] = [];
                    }
                    dateGroups[date].push(item);
                });

                const totalDays = Object.keys(dateGroups).length;
                const totalRecords = validData.length;

                // 1. 基本统计指标
                const avgDailyIncome = totalDays > 0 ? summaryData.total_income / totalDays : 0;
                const avgDailyExpense = totalDays > 0 ? Math.abs(summaryData.total_expense) / totalDays : 0;

                document.getElementById('avgDailyIncome').textContent = '¥' + avgDailyIncome.toFixed(2);
                document.getElementById('avgDailyExpense').textContent = '¥' + avgDailyExpense.toFixed(2);
                document.getElementById('totalDays').textContent = totalDays;
                document.getElementById('totalRecords').textContent = totalRecords;

                // 2. 收支比例分析
                const totalAmount = Math.abs(summaryData.total_income) + Math.abs(summaryData.total_expense);
                const incomeRatio = totalAmount > 0 ? (Math.abs(summaryData.total_income) / totalAmount * 100) : 50;
                const expenseRatio = totalAmount > 0 ? (Math.abs(summaryData.total_expense) / totalAmount * 100) : 50;
                const savingsRate = summaryData.total_income > 0 ? (summaryData.balance / summaryData.total_income * 100) : 0;

                document.getElementById('incomeProgress').style.width = incomeRatio + '%';
                document.getElementById('expenseProgress').style.width = expenseRatio + '%';
                document.getElementById('incomeRatio').textContent = incomeRatio.toFixed(1) + '%';
                document.getElementById('expenseRatio').textContent = expenseRatio.toFixed(1) + '%';
                document.getElementById('savingsRate').textContent = savingsRate.toFixed(1) + '%';

                // 3. 交易极值分析
                let maxIncome = { amount: 0, date: '-' };
                let maxExpense = { amount: 0, date: '-' };
                let totalAmount2 = 0;

                validData.forEach(item => {
                    const amount = parseFloat(item.金额);
                    const date = new Date(item.记录时间).toLocaleDateString('zh-CN');
                    
                    totalAmount2 += Math.abs(amount);
                    
                    if (amount > maxIncome.amount) {
                        maxIncome = { amount: amount, date: date };
                    }
                    
                    if (amount < 0 && Math.abs(amount) > maxExpense.amount) {
                        maxExpense = { amount: Math.abs(amount), date: date };
                    }
                });

                const avgAmount = totalRecords > 0 ? totalAmount2 / totalRecords : 0;

                document.getElementById('maxIncome').textContent = '¥' + maxIncome.amount.toFixed(2);
                document.getElementById('maxIncomeDate').textContent = maxIncome.date;
                document.getElementById('maxExpense').textContent = '¥' + maxExpense.amount.toFixed(2);
                document.getElementById('maxExpenseDate').textContent = maxExpense.date;
                document.getElementById('avgAmount').textContent = '¥' + avgAmount.toFixed(2);

                // 4. 活跃度分析
                let mostActiveDate = { date: '-', count: 0 };
                let consecutiveDays = 0;
                let maxConsecutive = 0;
                let lastDate = null;

                const sortedDates = Object.keys(dateGroups).sort();
                
                // 找到最活跃的日期
                Object.entries(dateGroups).forEach(([date, transactions]) => {
                    if (transactions.length > mostActiveDate.count) {
                        mostActiveDate = { 
                            date: new Date(date).toLocaleDateString('zh-CN'), 
                            count: transactions.length 
                        };
                    }
                });

                // 计算最长连续记录天数
                sortedDates.forEach(dateStr => {
                    const currentDate = new Date(dateStr);
                    if (lastDate) {
                        const diffDays = (currentDate - lastDate) / (1000 * 60 * 60 * 24);
                        if (diffDays === 1) {
                            consecutiveDays++;
                        } else {
                            maxConsecutive = Math.max(maxConsecutive, consecutiveDays);
                            consecutiveDays = 1;
                        }
                    } else {
                        consecutiveDays = 1;
                    }
                    lastDate = currentDate;
                });
                maxConsecutive = Math.max(maxConsecutive, consecutiveDays);

                const avgDailyTransactions = totalDays > 0 ? (totalRecords / totalDays) : 0;

                document.getElementById('mostActiveDate').textContent = mostActiveDate.date;
                document.getElementById('mostActiveDateCount').textContent = mostActiveDate.count + '笔交易';
                document.getElementById('consecutiveDays').textContent = maxConsecutive + '天';
                document.getElementById('avgDailyTransactions').textContent = avgDailyTransactions.toFixed(1) + '笔';

                // 5. 月度对比分析
                updateMonthlyComparison(summaryData);
                
                // 6. 初始化概览页面图表
                initializeOverviewCharts(summaryData);

                console.log('详细统计数据计算完成');
            }

            // 更新月度对比数据
            function updateMonthlyComparison(summaryData) {
                const monthlyData = summaryData.monthly_summary || {};
                const monthlyContainer = document.getElementById('monthlyComparison');
                monthlyContainer.innerHTML = '';

                if (Object.keys(monthlyData).length === 0) {
                    monthlyContainer.innerHTML = '<div class="col-12 text-center text-muted">暂无月度数据</div>';
                    return;
                }

                const sortedMonths = Object.keys(monthlyData).sort();
                
                sortedMonths.forEach((month, index) => {
                    const data = monthlyData[month];
                    const income = data.income || 0;
                    const expense = Math.abs(data.expense || 0);
                    const balance = income - expense;
                    
                    // 计算环比增长（与上个月对比）
                    let incomeGrowth = '';
                    let expenseGrowth = '';
                    
                    if (index > 0) {
                        const prevMonth = sortedMonths[index - 1];
                        const prevData = monthlyData[prevMonth];
                        const prevIncome = prevData.income || 0;
                        const prevExpense = Math.abs(prevData.expense || 0);
                        
                        if (prevIncome > 0) {
                            const incomeGrowthRate = ((income - prevIncome) / prevIncome * 100);
                            incomeGrowth = incomeGrowthRate >= 0 ? 
                                `<small class="text-success">+${incomeGrowthRate.toFixed(1)}%</small>` :
                                `<small class="text-danger">${incomeGrowthRate.toFixed(1)}%</small>`;
                        }
                        
                        if (prevExpense > 0) {
                            const expenseGrowthRate = ((expense - prevExpense) / prevExpense * 100);
                            expenseGrowth = expenseGrowthRate >= 0 ? 
                                `<small class="text-danger">+${expenseGrowthRate.toFixed(1)}%</small>` :
                                `<small class="text-success">${expenseGrowthRate.toFixed(1)}%</small>`;
                        }
                    }

                    const monthDiv = document.createElement('div');
                    monthDiv.className = 'col-lg-3 col-md-4 col-sm-6 mb-3';
                    monthDiv.innerHTML = `
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <h6 class="card-title text-primary">${month}</h6>
                                <div class="mb-2">
                                    <div class="text-success fw-bold">收入: ¥${income.toFixed(2)}</div>
                                    ${incomeGrowth}
                                </div>
                                <div class="mb-2">
                                    <div class="text-danger fw-bold">支出: ¥${expense.toFixed(2)}</div>
                                    ${expenseGrowth}
                                </div>
                                <hr class="my-2">
                                <div class="${balance >= 0 ? 'text-success' : 'text-danger'} fw-bold">
                                    结余: ¥${balance.toFixed(2)}
                                </div>
                            </div>
                        </div>
                    `;
                    monthlyContainer.appendChild(monthDiv);
                });
            }

            // 上传记录管理功能 - 将关键函数定义为全局函数
            window.loadUploadRecords = function() {
                console.log('开始加载上传记录...');
                console.log('当前URL:', window.location.href);
                
                fetch('/api/upload_records')
                    .then(response => {
                        console.log('API响应状态:', response.status);
                        console.log('API响应headers:', response.headers);
                        return response.json();
                    })
                    .then(data => {
                        console.log('上传记录数据:', data);
                        if (data.error) {
                            console.error('API返回错误:', data.error);
                            showRecordError(data.error);
                            return;
                        }
                        displayUploadRecords(data);
                    })
                    .catch(error => {
                        console.error('获取上传记录失败:', error);
                        showRecordError('获取上传记录失败，请重试');
                    });
            };

            window.displayUploadRecords = function(records) {
                const tableBody = document.getElementById('uploadRecordsTableBody');
                const noRecordsMessage = document.getElementById('noRecordsMessage');
                
                if (!records || records.length === 0) {
                    tableBody.innerHTML = '';
                    noRecordsMessage.style.display = 'block';
                    return;
                }
                
                noRecordsMessage.style.display = 'none';
                
                const recordsHtml = records.map(record => {
                    const uploadTime = new Date(record.upload_time).toLocaleString('zh-CN');
                    const fileSize = formatFileSize(record.file_size);
                    const statusBadge = record.status === 'success' ? 
                        '<span class="badge bg-success">成功</span>' : 
                        '<span class="badge bg-danger">失败</span>';
                    
                    return `
                        <tr>
                            <td>${uploadTime}</td>
                            <td>${record.filename}</td>
                            <td>${fileSize}</td>
                            <td>${record.record_count}</td>
                            <td>${statusBadge}</td>
                            <td>
                                <button class="btn btn-danger btn-sm" onclick="deleteUploadRecord(${record.id})">
                                    <i class="bi bi-trash"></i> 删除
                                </button>
                            </td>
                        </tr>
                    `;
                }).join('');
                
                tableBody.innerHTML = recordsHtml;
            };

            window.formatFileSize = function(bytes) {
                if (bytes < 1024) return bytes + ' B';
                else if (bytes < 1048576) return (bytes / 1024).toFixed(2) + ' KB';
                else return (bytes / 1048576).toFixed(2) + ' MB';
            };

            window.deleteUploadRecord = function(recordId) {
                if (!confirm('确定要删除这条上传记录吗？\n注意：删除后，对应的账单数据也将被清空！')) {
                    return;
                }
                
                console.log('删除上传记录:', recordId);
                fetch(`/api/delete_upload_record/${recordId}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        showRecordError(data.error);
                    } else {
                        showRecordSuccess(data.message);
                        // 重新加载记录列表
                        window.loadUploadRecords();
                        
                        // 如果当前在其他页面显示数据，需要刷新页面以反映数据变化
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    }
                })
                .catch(error => {
                    console.error('删除上传记录失败:', error);
                    showRecordError('删除失败，请重试');
                });
            };

            function showRecordError(message) {
                const errorAlert = document.getElementById('recordErrorMessage');
                const successAlert = document.getElementById('recordSuccessMessage');
                
                successAlert.style.display = 'none';
                errorAlert.textContent = message;
                errorAlert.style.display = 'block';
                
                setTimeout(() => {
                    errorAlert.style.display = 'none';
                }, 5000);
            }

            function showRecordSuccess(message) {
                const errorAlert = document.getElementById('recordErrorMessage');
                const successAlert = document.getElementById('recordSuccessMessage');
                
                errorAlert.style.display = 'none';
                successAlert.textContent = message;
                successAlert.style.display = 'block';
                
                setTimeout(() => {
                    successAlert.style.display = 'none';
                }, 3000);
            }

            // 图表初始化管理器
            const chartInitializers = {
                'category-analysis': () => {
                    if (chartData.category_summary) initCategoryChart(chartData.category_summary);
                    if (chartData.type_summary) initTypeChart(chartData.type_summary);
                },
                'trend-analysis': () => {
                    if (chartData.daily_trend) initTrendChart(chartData.daily_trend);
                    if (chartData.monthly_summary) initMonthlyChart(chartData.monthly_summary);
                    if (chartData.cumulative_trend) initCumulativeChart(chartData.cumulative_trend);
                },
                'account-analysis': () => {
                    if (chartData.account_summary) initAccountChart(chartData.account_summary);
                },
                'time-analysis': () => {
                    if (chartData.weekly_summary) initWeeklyHeatmap(chartData.weekly_summary);
                    if (chartData.hourly_summary) initHourlyChart(chartData.hourly_summary);
                },
                'comprehensive-analysis': () => {
                    if (chartData.category_summary && chartData.type_summary) {
                        initRadarChart(chartData.category_summary, chartData.type_summary);
                    }
                },
                'ranking': () => {
                    // 排行榜图表初始化
                    if (window.rankingDataLoaded && typeof initRankingChart === 'function') {
                        setTimeout(() => {
                            initRankingChart(currentRankingType);
                        }, 100);
                    }
                },
                'xianyuearnings': () => {
                    // 闲鱼收益图表初始化
                    if (allData && allData.length > 0) {
                        setTimeout(() => {
                            initXianyuEarningsAnalysis();
                        }, 100);
                    }
                },
                'upload-records': () => {
                    // 上传记录页面初始化
                    console.log('初始化上传记录页面');
                    window.loadUploadRecords();
                }
            };

            // 已初始化的图表区域记录
            const initializedSections = new Set();

            // 初始化指定区域的图表
            function initializeSectionCharts(sectionId) {
                console.log(`初始化区域图表: ${sectionId}`);

                if (initializedSections.has(sectionId)) {
                    // 如果已经初始化过，只需要重新调整大小
                    console.log(`区域 ${sectionId} 已初始化，重新调整大小`);
                    setTimeout(() => {
                        resizeChartsInSection(sectionId);
                    }, 100);
                    return;
                }

                if (chartInitializers[sectionId]) {
                    console.log(`开始初始化区域 ${sectionId} 的图表`);
                    setTimeout(() => {
                        try {
                            chartInitializers[sectionId]();
                            initializedSections.add(sectionId);
                            console.log(`区域 ${sectionId} 图表初始化完成`);
                        } catch (error) {
                            console.error(`初始化区域 ${sectionId} 图表时出错:`, error);
                        }
                    }, 100);
                } else {
                    console.log(`区域 ${sectionId} 没有对应的图表初始化器`);
                }
            }
            
            // 获取明细数据
            let allData = [];
            let currentPage = 1;
            const pageSize = 20;
            
            fetch('/api/data')
                .then(response => response.json())
                .then(data => {
                    allData = data;
                    
                    // 填充过滤器选项
                    fillFilterOptions(data);
                    
                    // 首次加载数据
                    updateTableData();

                    // 如果汇总数据已经加载，更新概览统计
                    if (chartData && Object.keys(chartData).length > 0) {
                        updateOverviewStatistics(chartData);
                    }
                })
                .catch(error => {
                    console.error('获取明细数据出错:', error);
                });
            
            // 搜索和重置按钮
            document.getElementById('searchBtn').addEventListener('click', function() {
                currentPage = 1;
                updateTableData();
            });
            
            document.getElementById('resetBtn').addEventListener('click', function() {
                document.getElementById('searchText').value = '';
                document.getElementById('categoryFilter').value = '';
                document.getElementById('typeFilter').value = '';
                currentPage = 1;
                updateTableData();
            });
            
            // 填充过滤器选项
            function fillFilterOptions(data) {
                const categorySet = new Set();
                const typeSet = new Set();
                
                data.forEach(item => {
                    if (item.分类) categorySet.add(item.分类);
                    if (item.收支类型) typeSet.add(item.收支类型);
                });
                
                const categoryFilter = document.getElementById('categoryFilter');
                categorySet.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category;
                    option.textContent = category;
                    categoryFilter.appendChild(option);
                });
                
                const typeFilter = document.getElementById('typeFilter');
                typeSet.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type;
                    option.textContent = type;
                    typeFilter.appendChild(option);
                });
            }
            
            // 更新表格数据
            function updateTableData() {
                const searchText = document.getElementById('searchText').value.toLowerCase();
                const categoryFilter = document.getElementById('categoryFilter').value;
                const typeFilter = document.getElementById('typeFilter').value;
                
                // 过滤数据
                const filteredData = allData.filter(item => {
                    const matchSearch = item.备注 && item.备注.toLowerCase().includes(searchText);
                    const matchCategory = !categoryFilter || item.分类 === categoryFilter;
                    const matchType = !typeFilter || item.收支类型 === typeFilter;
                    return matchSearch && matchCategory && matchType;
                });
                
                // 分页
                const start = (currentPage - 1) * pageSize;
                const end = start + pageSize;
                const pageData = filteredData.slice(start, end);
                
                // 更新表格
                const tableBody = document.getElementById('dataTableBody');
                tableBody.innerHTML = '';
                
                pageData.forEach(item => {
                    const tr = document.createElement('tr');
                    
                    // 格式化时间
                    const date = new Date(item.记录时间);
                    const formattedDate = date.toLocaleString('zh-CN');
                    
                    // 设置行样式
                    if (item.收支类型 && item.收支类型.includes('收入')) {
                        tr.classList.add('table-success');
                    } else if (item.收支类型 && item.收支类型.includes('支出')) {
                        tr.classList.add('table-danger');
                    }
                    
                    tr.innerHTML = `
                        <td>${formattedDate}</td>
                        <td>${item.分类 || '-'}</td>
                        <td>${item.收支类型 || '-'}</td>
                        <td>¥${item.金额 ? item.金额.toFixed(2) : '0.00'}</td>
                        <td>${item.备注 || '-'}</td>
                        <td>${item.账户 || '-'}</td>
                        <td>${item.来源 || '-'}</td>
                    `;
                    
                    tableBody.appendChild(tr);
                });
                
                // 更新分页
                updatePagination(filteredData.length);
            }
            
            // 更新分页控件
            function updatePagination(totalItems) {
                const totalPages = Math.ceil(totalItems / pageSize);
                const pagination = document.getElementById('pagination');
                pagination.innerHTML = '';
                
                // 添加上一页按钮
                const prevLi = document.createElement('li');
                prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
                prevLi.innerHTML = '<a class="page-link" href="#" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a>';
                prevLi.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (currentPage > 1) {
                        currentPage--;
                        updateTableData();
                    }
                });
                pagination.appendChild(prevLi);
                
                // 添加页码按钮
                const maxPageButtons = 5;
                const startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
                const endPage = Math.min(totalPages, startPage + maxPageButtons - 1);
                
                for (let i = startPage; i <= endPage; i++) {
                    const li = document.createElement('li');
                    li.className = `page-item ${i === currentPage ? 'active' : ''}`;
                    li.innerHTML = `<a class="page-link" href="#">${i}</a>`;
                    li.addEventListener('click', function(e) {
                        e.preventDefault();
                        currentPage = i;
                        updateTableData();
                    });
                    pagination.appendChild(li);
                }
                
                // 添加下一页按钮
                const nextLi = document.createElement('li');
                nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
                nextLi.innerHTML = '<a class="page-link" href="#" aria-label="Next"><span aria-hidden="true">&raquo;</span></a>';
                nextLi.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (currentPage < totalPages) {
                        currentPage++;
                        updateTableData();
                    }
                });
                pagination.appendChild(nextLi);
            }
            
            // 初始化分类饼图
            function initCategoryChart(categoryData) {
                const chartDom = document.getElementById('categoryPieChart');
                const myChart = echarts.init(chartDom);



                if (!categoryData || Object.keys(categoryData).length === 0) {
                    myChart.setOption({
                        title: {
                            text: '暂无支出数据',
                            left: 'center',
                            top: 'center',
                            textStyle: {
                                color: '#999',
                                fontSize: 16
                            }
                        }
                    });
                    return;
                }

                // 转换数据为echarts需要的格式，优化数据过滤逻辑
                const seriesData = [];
                for (const [category, amount] of Object.entries(categoryData)) {
                    const numAmount = parseFloat(amount);
                    // 显示所有有意义的支出数据（包括负数和正数，但排除接近0的值）
                    if (Math.abs(numAmount) > 0.01) {
                        // 如果是负数，说明是支出，取绝对值
                        // 如果是正数，可能是某些特殊分类的支出，也包含进来
                        seriesData.push({
                            name: category || '未分类',
                            value: Math.abs(numAmount)
                        });
                    }
                }

                if (seriesData.length === 0) {
                    myChart.setOption({
                        title: {
                            text: '暂无支出数据',
                            left: 'center',
                            top: 'center',
                            textStyle: {
                                color: '#999',
                                fontSize: 16
                            }
                        }
                    });
                    return;
                }

                // 对数据进行排序
                seriesData.sort((a, b) => b.value - a.value);

                // 只显示前8个类别，其余归为"其他"
                let others = 0;
                const top8 = seriesData.slice(0, 8);

                if (seriesData.length > 8) {
                    for (let i = 8; i < seriesData.length; i++) {
                        others += seriesData[i].value;
                    }
                    if (others > 0) {
                        top8.push({
                            name: '其他',
                            value: others
                        });
                    }
                }

                // 定义更丰富的颜色方案
                const colors = [
                    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4',
                    '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE'
                ];

                const option = {
                    tooltip: {
                        trigger: 'item',
                        formatter: function(params) {
                            return `${params.seriesName}<br/>${params.name}: ¥${params.value.toFixed(2)} (${params.percent}%)`;
                        },
                        backgroundColor: 'rgba(50,50,50,0.9)',
                        borderColor: '#777',
                        borderWidth: 1,
                        textStyle: {
                            color: '#fff'
                        }
                    },
                    legend: {
                        orient: 'vertical',
                        right: '5%',
                        top: 'center',
                        data: top8.map(item => item.name),
                        textStyle: {
                            fontSize: 12
                        },
                        itemWidth: 14,
                        itemHeight: 14
                    },
                    series: [
                        {
                            name: '支出分类',
                            type: 'pie',
                            radius: ['45%', '75%'],
                            center: ['40%', '50%'],
                            avoidLabelOverlap: false,
                            itemStyle: {
                                borderRadius: 8,
                                borderColor: '#fff',
                                borderWidth: 2
                            },
                            label: {
                                show: true,
                                position: 'outside',
                                formatter: '{b}\n{d}%',
                                fontSize: 11,
                                color: '#666'
                            },
                            emphasis: {
                                label: {
                                    show: true,
                                    fontSize: 14,
                                    fontWeight: 'bold'
                                },
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            },
                            labelLine: {
                                show: true,
                                length: 15,
                                length2: 10
                            },
                            data: top8,
                            color: colors
                        }
                    ]
                };

                myChart.setOption(option);

                // 响应窗口大小变化
                window.addEventListener('resize', function() {
                    myChart.resize();
                });
            }
            
            // 初始化收支类型饼图
            function initTypeChart(typeData) {
                const chartDom = document.getElementById('typePieChart');
                const myChart = echarts.init(chartDom);

                if (!typeData || Object.keys(typeData).length === 0) {
                    myChart.setOption({
                        title: {
                            text: '暂无收支数据',
                            left: 'center',
                            top: 'center',
                            textStyle: {
                                color: '#999',
                                fontSize: 16
                            }
                        }
                    });
                    return;
                }

                // 转换数据为echarts需要的格式，并按类型分类
                const seriesData = [];
                const typeColors = {
                    '收入': '#4CAF50',        // 清新绿色 - 代表收入增长
                    '支出': '#FF7043',        // 温暖橙红 - 比纯红色更柔和
                    '内部收入': '#42A5F5',    // 天空蓝 - 清爽明亮
                    '内部支出': '#FFA726',    // 琥珀橙 - 温暖但不刺眼
                    '资金收入': '#AB47BC',    // 优雅紫 - 高端感
                    '资金支出': '#EC407A',    // 玫瑰粉 - 柔和的粉色
                    '投资收入': '#26A69A',    // 青绿色 - 稳重可靠
                    '投资支出': '#FFCA28',    // 金黄色 - 明亮但不刺眼
                    '不计收支': '#78909C'     // 蓝灰色 - 中性且现代
                };

                for (const [type, amount] of Object.entries(typeData)) {
                    const numAmount = parseFloat(amount);
                    if (Math.abs(numAmount) > 0.01) {
                        const baseColor = typeColors[type] || '#95a5a6';
                        seriesData.push({
                            name: type || '未知类型',
                            value: Math.abs(numAmount),
                            itemStyle: {
                                color: {
                                    type: 'linear',
                                    x: 0, y: 0, x2: 1, y2: 1,
                                    colorStops: [
                                        { offset: 0, color: baseColor },
                                        { offset: 1, color: baseColor + 'CC' } // 添加透明度
                                    ]
                                },
                                borderColor: '#fff',
                                borderWidth: 3,
                                shadowBlur: 8,
                                shadowColor: baseColor + '40'
                            }
                        });
                    }
                }

                if (seriesData.length === 0) {
                    myChart.setOption({
                        title: {
                            text: '暂无收支数据',
                            left: 'center',
                            top: 'center',
                            textStyle: {
                                color: '#999',
                                fontSize: 16
                            }
                        }
                    });
                    return;
                }

                // 按金额排序
                seriesData.sort((a, b) => b.value - a.value);

                const option = {
                    tooltip: {
                        trigger: 'item',
                        formatter: function(params) {
                            return `${params.seriesName}<br/>${params.name}: ¥${params.value.toFixed(2)} (${params.percent}%)`;
                        },
                        backgroundColor: 'rgba(50,50,50,0.9)',
                        borderColor: '#777',
                        borderWidth: 1,
                        textStyle: {
                            color: '#fff'
                        }
                    },
                    legend: {
                        orient: 'vertical',
                        right: '5%',
                        top: 'center',
                        data: seriesData.map(item => item.name),
                        textStyle: {
                            fontSize: 12
                        },
                        itemWidth: 14,
                        itemHeight: 14
                    },
                    series: [
                        {
                            name: '收支类型',
                            type: 'pie',
                            radius: ['50%', '80%'],
                            center: ['40%', '50%'],
                            avoidLabelOverlap: false,
                            label: {
                                show: true,
                                position: 'outside',
                                formatter: function(params) {
                                    // 根据类型显示不同的图标
                                    let icon = '';
                                    if (params.name.includes('收入')) {
                                        icon = '💰 ';
                                    } else if (params.name.includes('支出')) {
                                        icon = '💸 ';
                                    } else {
                                        icon = '📊 ';
                                    }
                                    return icon + params.name + '\n' + params.percent + '%';
                                },
                                fontSize: 12,
                                color: '#555',
                                fontWeight: '500'
                            },
                            emphasis: {
                                label: {
                                    show: true,
                                    fontSize: 15,
                                    fontWeight: 'bold',
                                    color: '#333'
                                },
                                itemStyle: {
                                    shadowBlur: 15,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.3)'
                                },
                                scale: true,
                                scaleSize: 5
                            },
                            labelLine: {
                                show: true,
                                length: 20,
                                length2: 15,
                                lineStyle: {
                                    color: '#999',
                                    width: 2
                                }
                            },
                            data: seriesData
                        }
                    ]
                };

                myChart.setOption(option);

                // 响应窗口大小变化
                window.addEventListener('resize', function() {
                    myChart.resize();
                });
            }
            
            // 初始化趋势图
            function initTrendChart(trendData) {
                const chartDom = document.getElementById('trendChart');
                const myChart = echarts.init(chartDom);
                
                // 转换数据为echarts需要的格式
                const dates = Object.keys(trendData).sort();
                const values = dates.map(date => trendData[date]);
                
                const option = {
                    tooltip: {
                        trigger: 'axis',
                        formatter: function(params) {
                            return `${params[0].axisValue}<br/>金额: ¥${params[0].data.toFixed(2)}`;
                        }
                    },
                    xAxis: {
                        type: 'category',
                        data: dates
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: {
                            formatter: '¥{value}'
                        }
                    },
                    series: [
                        {
                            data: values,
                            type: 'line',
                            smooth: true,
                            lineStyle: {
                                width: 3
                            },
                            markPoint: {
                                data: [
                                    { type: 'max', name: '最大值' },
                                    { type: 'min', name: '最小值' }
                                ]
                            }
                        }
                    ]
                };
                
                myChart.setOption(option);
                
                // 响应窗口大小变化
                window.addEventListener('resize', function() {
                    myChart.resize();
                });
            }
            
            // 获取排行榜数据
            let rankingData = {
                income: [],
                expense: []
            };
            let currentRankingType = 'expense';  // 默认显示支出排行榜
            let currentSortOrder = 'asc';  // 默认升序排列 ('desc' 或 'asc')
            let rankingChart = null; // 全局变量保存图表实例

            // 显示加载状态
            function showRankingLoading() {
                document.getElementById('rankingLoading').style.display = 'block';
                document.getElementById('rankingChart').style.display = 'none';
                document.getElementById('rankingError').style.display = 'none';
            }

            // 显示排行榜图表
            function showRankingChart() {
                document.getElementById('rankingLoading').style.display = 'none';
                document.getElementById('rankingChart').style.display = 'block';
                document.getElementById('rankingError').style.display = 'none';
            }

            // 显示排行榜错误
            function showRankingError(message) {
                document.getElementById('rankingLoading').style.display = 'none';
                document.getElementById('rankingChart').style.display = 'none';
                document.getElementById('rankingError').style.display = 'block';
                document.getElementById('rankingError').textContent = message;
            }

            // 更新排行榜计数
            function updateRankingCount(type) {
                const count = rankingData[type] ? rankingData[type].length : 0;
                document.getElementById('rankingCount').textContent = `${count}项`;
            }

            // 添加调试信息
            console.log("开始获取排行榜数据...");
            showRankingLoading();

            fetch('/api/ranking')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    console.log("获取排行榜数据成功，正在解析JSON...");
                    return response.json();
                })
                .then(data => {
                    console.log("排行榜数据解析完成:", data);
                    rankingData = data;
                    window.rankingDataLoaded = true;

                    // 检查数据完整性
                    if (!data.income || !data.expense) {
                        console.error("排行榜数据格式不正确:", data);
                        showRankingError("数据格式不正确，无法显示排行榜");
                        return;
                    }

                    console.log(`收入数据项数: ${data.income.length}, 支出数据项数: ${data.expense.length}`);
                    updateRankingCount('expense'); // 默认显示支出排行榜计数

                    // 设置排行榜事件监听器
                    setupRankingEvents();
                    updateRankingTitle();
                    updateSortButton();

                    // 只有当排行榜区域是活动状态时才初始化图表
                    const rankingSection = document.getElementById('ranking');
                    if (rankingSection && rankingSection.classList.contains('active')) {
                        setTimeout(() => {
                            initRankingChart(currentRankingType);
                        }, 100);
                    }
                })
                .catch(error => {
                    console.error('获取排行榜数据出错:', error);
                    showRankingError(`获取排行榜数据失败: ${error.message}`);
                });
            
            // 重写排行榜初始化函数
            function initRankingChart(type) {
                console.log(`初始化${type}排行榜...`);
                const chartDom = document.getElementById('rankingChart');
                if (!chartDom) {
                    console.error('找不到rankingChart元素');
                    return;
                }
                
                // 显示加载状态
                showRankingLoading();
                
                try {
                    // 销毁之前的实例
                    if (rankingChart) {
                        rankingChart.dispose();
                        rankingChart = null;
                    }
                    
                    // 根据数据量动态设置图表高度
                    const dataLength = rankingData[type] ? rankingData[type].length : 0;
                    const minHeight = 600;
                    const itemHeight = 40; // 每个条目的高度
                    const dynamicHeight = Math.max(minHeight, dataLength * itemHeight + 200);
                    chartDom.style.height = dynamicHeight + 'px';
                    chartDom.style.width = '100%';
                    
                    // 如果没有数据，显示空图表
                    if (!rankingData[type] || rankingData[type].length === 0) {
                        console.log(`${type}排行榜没有数据`);
                        showRankingError(`没有${type === 'income' ? '收入' : '支出'}数据可供显示`);
                        return;
                    }
                    
                    // 准备数据
                    console.log(`处理${type}排行榜数据...`);
                    const sortedData = [...rankingData[type]];

                    // 根据当前排序方式对数据进行排序
                    if (currentSortOrder === 'asc') {
                        sortedData.sort((a, b) => a.金额 - b.金额);  // 升序：从小到大
                    } else {
                        sortedData.sort((a, b) => b.金额 - a.金额);  // 降序：从大到小
                    }
                    console.log(`${currentSortOrder === 'asc' ? '升序' : '降序'}排序后的${type}数据:`, sortedData);

                    // 显示所有数据（不限制数量）
                    console.log(`显示所有 ${sortedData.length} 项${type}数据`);

                    // 反转数组，让柱状图从下到上排序
                    const reversedData = [...sortedData].reverse();
                    
                    // 从数据中提取类别和值
                    const categories = reversedData.map(item => item.处理后备注 || '未知类别');
                    const values = reversedData.map(item => {
                        let amount = 0;
                        
                        try {
                            if (typeof item.金额 === 'number') {
                                amount = parseFloat(item.金额.toFixed(2));
                            } else if (typeof item.金额 === 'string') {
                                amount = parseFloat(parseFloat(item.金额).toFixed(2));
                            }
                        } catch (e) {
                            console.error('处理金额时出错:', e);
                        }
                        
                        return amount || 0;
                    });
                    
                    console.log(`${type}排行榜类别:`, categories);
                    console.log(`${type}排行榜数值:`, values);
                    
                    // 计算最大值，用于设置坐标轴
                    const maxValue = Math.max(...values, 1);  // 至少为1，避免全0的情况
                    
                    // 显示图表容器
                    showRankingChart();
                    
                    // 重新初始化ECharts实例
                    setTimeout(() => {
                        rankingChart = echarts.init(chartDom);
                        console.log("ECharts实例创建成功");
                        
                        // 设置图表选项
                        const option = {
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: {
                                    type: 'shadow'
                                },
                                formatter: function(params) {
                                    const data = params[0];
                                    return `${data.name}<br/>${type === 'income' ? '收入' : '支出'}: ¥${data.value.toFixed(2)}`;
                                }
                            },
                            legend: {
                                data: [type === 'income' ? '收入' : '支出'],
                                bottom: '5%'
                            },
                            grid: {
                                left: '15%',
                                right: '20%',
                                bottom: '10%',
                                top: '2%',
                                containLabel: true
                            },
                            xAxis: {
                                type: 'value',
                                position: 'top',
                                name: '金额(元)',
                                nameTextStyle: {
                                    color: '#666',
                                    fontSize: 12
                                },
                                axisLabel: {
                                    formatter: function(value) {
                                        if (value >= 10000) {
                                            return '¥' + (value / 10000).toFixed(1) + 'w';
                                        } else if (value >= 1000) {
                                            return '¥' + (value / 1000).toFixed(1) + 'k';
                                        }
                                        return '¥' + value.toFixed(0);
                                    },
                                    color: '#666',
                                    fontSize: 11
                                },
                                axisLine: {
                                    lineStyle: {
                                        color: '#e0e0e0'
                                    }
                                },
                                splitLine: {
                                    lineStyle: {
                                        color: '#f0f0f0',
                                        type: 'dashed'
                                    }
                                },
                                max: Math.ceil(maxValue * 1.1)
                            },
                            yAxis: {
                                type: 'category',
                                data: categories,
                                inverse: true,
                                axisLabel: {
                                    formatter: function(value) {
                                        if (value.length > 12) {
                                            return value.substring(0, 12) + '...';
                                        }
                                        return value;
                                    },
                                    interval: 0,
                                    rotate: 0,
                                    fontSize: 11,
                                    color: '#666'
                                },
                                axisLine: {
                                    lineStyle: {
                                        color: '#e0e0e0'
                                    }
                                },
                                axisTick: {
                                    show: false
                                }
                            },
                            series: [
                                {
                                    name: type === 'income' ? '收入' : '支出',
                                    type: 'bar',
                                    data: values.map((value, index) => ({
                                        value: value,
                                        itemStyle: {
                                            color: {
                                                type: 'linear',
                                                x: 0, y: 0, x2: 1, y2: 0,
                                                colorStops: type === 'income' ? [
                                                    { offset: 0, color: '#4CAF50' },
                                                    { offset: 1, color: '#81C784' }
                                                ] : [
                                                    { offset: 0, color: '#FF7043' },
                                                    { offset: 1, color: '#FFAB91' }
                                                ]
                                            },
                                            borderRadius: [0, 6, 6, 0],
                                            shadowBlur: 4,
                                            shadowColor: 'rgba(0, 0, 0, 0.1)',
                                            shadowOffsetX: 2,
                                            shadowOffsetY: 2
                                        }
                                    })),
                                    label: {
                                        show: true,
                                        position: 'right',
                                        formatter: function(params) {
                                            return '¥' + params.value.toFixed(2);
                                        },
                                        fontSize: 11,
                                        color: '#555',
                                        fontWeight: '500'
                                    },
                                    emphasis: {
                                        itemStyle: {
                                            shadowBlur: 8,
                                            shadowColor: 'rgba(0, 0, 0, 0.2)'
                                        }
                                    },
                                    barWidth: '70%'
                                }
                            ]
                        };
                        
                        console.log(`设置${type}排行榜图表选项:`, option);
                        
                        try {
                            rankingChart.setOption(option, true);
                            console.log(`${type}排行榜图表渲染完成`);
                            
                            // 额外的强制重绘步骤
                            setTimeout(() => {
                                if (rankingChart) {
                                    rankingChart.resize();
                                }
                            }, 200);
                        } catch (err) {
                            console.error(`渲染${type}排行榜时出错:`, err);
                            showRankingError(`渲染图表失败: ${err.message}`);
                        }
                    }, 100);
                    
                    // 响应窗口大小变化
                    window.addEventListener('resize', function() {
                        try {
                            if (rankingChart) {
                                rankingChart.resize();
                            }
                        } catch (err) {
                            console.error("图表调整大小时出错:", err);
                        }
                    });
                    
                    return rankingChart;
                } catch (error) {
                    console.error(`初始化${type}排行榜出错:`, error);
                    showRankingError(`初始化排行榜失败: ${error.message}`);
                    return null;
                }
            }
            
            // 更新排行榜标题
            function updateRankingTitle() {
                const typeText = currentRankingType === 'income' ? '收入排行榜' : '支出排行榜';
                const sortText = currentSortOrder === 'asc' ? '(升序)' : '(降序)';
                document.getElementById('rankingTitle').textContent = `${typeText} ${sortText}`;
            }

            // 更新排序按钮显示
            function updateSortButton() {
                const sortIcon = document.getElementById('sortIcon');
                const sortText = document.getElementById('sortText');

                if (sortIcon && sortText) {
                    if (currentSortOrder === 'asc') {
                        sortIcon.className = 'bi bi-sort-numeric-up';
                        sortText.textContent = '升序';
                    } else {
                        sortIcon.className = 'bi bi-sort-numeric-down';
                        sortText.textContent = '降序';
                    }
                }
            }

            // 设置排行榜相关事件
            function setupRankingEvents() {
                console.log("设置排行榜事件监听器");
                
                // 支出排行榜按钮点击事件
                const expenseBtn = document.getElementById('expenseRankingBtn');
                if (expenseBtn) {
                    expenseBtn.onclick = function(e) {
                        e.preventDefault(); // 阻止默认行为
                        console.log("点击了支出排行榜按钮");
                        expenseBtn.classList.add('active', 'btn-primary');
                        expenseBtn.classList.remove('btn-outline-primary');
                        
                        const incomeBtn = document.getElementById('incomeRankingBtn');
                        if (incomeBtn) {
                            incomeBtn.classList.remove('active', 'btn-primary');
                            incomeBtn.classList.add('btn-outline-primary');
                        }
                        
                        currentRankingType = 'expense';
                        updateRankingTitle();
                        updateSortButton();
                        updateRankingCount('expense');
                        initRankingChart('expense');
                        return false;
                    };
                } else {
                    console.error("找不到支出排行榜按钮元素");
                }
                
                // 收入排行榜按钮点击事件
                const incomeBtn = document.getElementById('incomeRankingBtn');
                if (incomeBtn) {
                    incomeBtn.onclick = function(e) {
                        e.preventDefault(); // 阻止默认行为
                        console.log("点击了收入排行榜按钮");
                        incomeBtn.classList.add('active', 'btn-primary');
                        incomeBtn.classList.remove('btn-outline-primary');
                        
                        const expenseBtn = document.getElementById('expenseRankingBtn');
                        if (expenseBtn) {
                            expenseBtn.classList.remove('active', 'btn-primary');
                            expenseBtn.classList.add('btn-outline-primary');
                        }
                        
                        currentRankingType = 'income';
                        updateRankingTitle();
                        updateSortButton();
                        updateRankingCount('income');
                        initRankingChart('income');
                        return false;
                    };
                } else {
                    console.error("找不到收入排行榜按钮元素");
                }
                
                // 注意：在新的布局中，我们不再使用Bootstrap标签页，而是使用自定义的导航系统
                // 排行榜的激活将通过导航系统的 initializeSectionCharts 函数来处理
                console.log("排行榜事件监听器设置完成，使用新的导航系统");

                // 排序切换按钮点击事件
                const sortToggleBtn = document.getElementById('sortToggleBtn');
                if (sortToggleBtn) {
                    sortToggleBtn.onclick = function(e) {
                        e.preventDefault();
                        console.log("点击了排序切换按钮");

                        // 切换排序状态
                        currentSortOrder = currentSortOrder === 'asc' ? 'desc' : 'asc';

                        // 更新按钮显示
                        updateSortButton();

                        // 更新标题并重新渲染图表
                        updateRankingTitle();
                        initRankingChart(currentRankingType);
                        return false;
                    };
                } else {
                    console.error("找不到排序切换按钮元素");
                }
            }

            // 初始化月度收支对比图
            function initMonthlyChart(monthlyData) {
                const chartDom = document.getElementById('monthlyChart');
                const myChart = echarts.init(chartDom);

                if (!monthlyData || Object.keys(monthlyData).length === 0) {
                    myChart.setOption({
                        title: {
                            text: '暂无数据',
                            left: 'center',
                            top: 'center'
                        }
                    });
                    return;
                }

                const months = Object.keys(monthlyData).sort();
                const incomeData = months.map(month => monthlyData[month].income || 0);
                const expenseData = months.map(month => Math.abs(monthlyData[month].expense || 0));

                const option = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        }
                    },
                    legend: {
                        data: ['收入', '支出']
                    },
                    xAxis: {
                        type: 'category',
                        data: months
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: {
                            formatter: '¥{value}'
                        }
                    },
                    series: [
                        {
                            name: '收入',
                            type: 'bar',
                            data: incomeData,
                            itemStyle: {
                                color: '#28a745'
                            }
                        },
                        {
                            name: '支出',
                            type: 'bar',
                            data: expenseData,
                            itemStyle: {
                                color: '#dc3545'
                            }
                        }
                    ]
                };

                myChart.setOption(option);
            }

            // 初始化账户分布饼图
            function initAccountChart(accountData) {
                const chartDom = document.getElementById('accountChart');
                const myChart = echarts.init(chartDom);

                if (!accountData || Object.keys(accountData).length === 0) {
                    myChart.setOption({
                        title: {
                            text: '暂无数据',
                            left: 'center',
                            top: 'center'
                        }
                    });
                    return;
                }

                const seriesData = Object.entries(accountData).map(([account, amount]) => ({
                    name: account || '未知账户',
                    value: Math.abs(amount)
                }));

                const option = {
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b}: ¥{c} ({d}%)'
                    },
                    legend: {
                        orient: 'vertical',
                        left: 'left'
                    },
                    series: [
                        {
                            name: '账户分布',
                            type: 'pie',
                            radius: '50%',
                            data: seriesData,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }
                    ]
                };

                myChart.setOption(option);
            }

            // 初始化周收支热力图
            function initWeeklyHeatmap(weeklyData) {
                const chartDom = document.getElementById('weeklyHeatmap');
                const myChart = echarts.init(chartDom);

                if (!weeklyData || weeklyData.length === 0) {
                    myChart.setOption({
                        title: {
                            text: '暂无数据',
                            left: 'center',
                            top: 'center'
                        }
                    });
                    return;
                }

                const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
                const hours = Array.from({length: 24}, (_, i) => i + '时');

                const option = {
                    tooltip: {
                        position: 'top',
                        formatter: function(params) {
                            return `${weekdays[params.data[1]]} ${hours[params.data[0]]}<br/>交易次数: ${params.data[2]}`;
                        }
                    },
                    grid: {
                        height: '50%',
                        top: '10%'
                    },
                    xAxis: {
                        type: 'category',
                        data: hours,
                        splitArea: {
                            show: true
                        }
                    },
                    yAxis: {
                        type: 'category',
                        data: weekdays,
                        splitArea: {
                            show: true
                        }
                    },
                    visualMap: {
                        min: 0,
                        max: Math.max(...weeklyData.map(item => item[2])),
                        calculable: true,
                        orient: 'horizontal',
                        left: 'center',
                        bottom: '15%'
                    },
                    series: [{
                        name: '交易活跃度',
                        type: 'heatmap',
                        data: weeklyData,
                        label: {
                            show: true
                        },
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }]
                };

                myChart.setOption(option);
            }

            // 初始化收支累计图
            function initCumulativeChart(cumulativeData) {
                const chartDom = document.getElementById('cumulativeChart');
                const myChart = echarts.init(chartDom);

                if (!cumulativeData || Object.keys(cumulativeData).length === 0) {
                    myChart.setOption({
                        title: {
                            text: '暂无数据',
                            left: 'center',
                            top: 'center'
                        }
                    });
                    return;
                }

                const dates = Object.keys(cumulativeData).sort();
                const values = dates.map(date => cumulativeData[date]);

                const option = {
                    tooltip: {
                        trigger: 'axis',
                        formatter: function(params) {
                            return `${params[0].axisValue}<br/>累计金额: ¥${params[0].data.toFixed(2)}`;
                        }
                    },
                    xAxis: {
                        type: 'category',
                        data: dates
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: {
                            formatter: '¥{value}'
                        }
                    },
                    series: [
                        {
                            data: values,
                            type: 'line',
                            smooth: true,
                            lineStyle: {
                                width: 3
                            },
                            areaStyle: {
                                opacity: 0.3
                            },
                            itemStyle: {
                                color: '#007bff'
                            }
                        }
                    ]
                };

                myChart.setOption(option);
            }

            // 初始化分类收支雷达图
            function initRadarChart(categoryData, typeData) {
                const chartDom = document.getElementById('radarChart');
                const myChart = echarts.init(chartDom);

                if (!categoryData || Object.keys(categoryData).length === 0) {
                    myChart.setOption({
                        title: {
                            text: '暂无数据',
                            left: 'center',
                            top: 'center'
                        }
                    });
                    return;
                }

                // 获取前6个最大的分类
                const sortedCategories = Object.entries(categoryData)
                    .sort((a, b) => Math.abs(b[1]) - Math.abs(a[1]))
                    .slice(0, 6);

                const indicator = sortedCategories.map(([category, _]) => ({
                    name: category,
                    max: Math.max(...sortedCategories.map(([_, amount]) => Math.abs(amount)))
                }));

                const data = sortedCategories.map(([_, amount]) => Math.abs(amount));

                const option = {
                    tooltip: {},
                    radar: {
                        indicator: indicator
                    },
                    series: [{
                        name: '分类金额',
                        type: 'radar',
                        data: [
                            {
                                value: data,
                                name: '支出分布'
                            }
                        ]
                    }]
                };

                myChart.setOption(option);
            }

            // 初始化时段分析柱状图
            function initHourlyChart(hourlyData) {
                const chartDom = document.getElementById('hourlyChart');
                const myChart = echarts.init(chartDom);

                if (!hourlyData || Object.keys(hourlyData).length === 0) {
                    myChart.setOption({
                        title: {
                            text: '暂无数据',
                            left: 'center',
                            top: 'center'
                        }
                    });
                    return;
                }

                const hours = Array.from({length: 24}, (_, i) => i + '时');
                const data = hours.map(hour => {
                    const hourKey = hour.replace('时', '');
                    return hourlyData[hourKey] || 0;
                });

                const option = {
                    tooltip: {
                        trigger: 'axis',
                        formatter: function(params) {
                            return `${params[0].axisValue}<br/>交易次数: ${params[0].data}`;
                        }
                    },
                    xAxis: {
                        type: 'category',
                        data: hours
                    },
                    yAxis: {
                        type: 'value'
                    },
                    series: [
                        {
                            data: data,
                            type: 'bar',
                            itemStyle: {
                                color: function(params) {
                                    // 根据时段设置不同颜色
                                    const hour = params.dataIndex;
                                    if (hour >= 6 && hour < 12) return '#ffc107'; // 上午
                                    if (hour >= 12 && hour < 18) return '#28a745'; // 下午
                                    if (hour >= 18 && hour < 22) return '#fd7e14'; // 晚上
                                    return '#6c757d'; // 深夜/凌晨
                                }
                            }
                        }
                    ]
                };

                myChart.setOption(option);
            }
            
            // 初始化闲鱼收益分析
            function initXianyuEarningsAnalysis() {
                console.log('开始初始化闲鱼收益分析...');
                
                if (!allData || allData.length === 0) {
                    console.error('没有数据可用于闲鱼收益分析');
                    return;
                }
                
                // 过滤闲鱼相关数据
                const xianyuData = allData.filter(item => {
                    const remark = item.备注 || '';
                    const category = item.分类 || '';
                    const type = item.收支类型 || '';
                    
                    return (
                        // 基础软件服务费：支出类型，备注包含"基础软件服务费"
                        (type.includes('支出') && remark.includes('基础软件服务费')) ||
                        // 闲鱼币智能软件服务费：支出类型，备注包含"闲鱼币智能软件服务费"
                        (type.includes('支出') && remark.includes('闲鱼币智能软件服务费')) ||
                        // 闲鱼超级擦亮：支出类型，备注包含"闲鱼超级擦亮"
                        (type.includes('支出') && remark.includes('闲鱼超级擦亮')) ||
                        // 生意收入：分类为"生意"且收支类型为收入
                        (category === '生意' && type.includes('收入'))
                    );
                });
                
                console.log(`找到${xianyuData.length}条闲鱼相关数据`, xianyuData);
                
                // 按日期分组计算每日数据
                const dailyData = {};
                let totalBasicFee = 0;
                let totalSmartFee = 0;
                let totalSuperBrightFee = 0;
                let totalBusinessIncome = 0;
                
                xianyuData.forEach(item => {
                    const date = new Date(item.记录时间).toISOString().split('T')[0];
                    const amount = parseFloat(item.金额) || 0;
                    const remark = item.备注 || '';
                    const category = item.分类 || '';
                    const type = item.收支类型 || '';
                    
                    if (!dailyData[date]) {
                        dailyData[date] = {
                            basicServiceFee: 0,
                            smartServiceFee: 0,
                            superBrightFee: 0,
                            businessIncome: 0,
                            netEarnings: 0
                        };
                    }
                    
                    if (type.includes('支出') && remark.includes('基础软件服务费')) {
                        const fee = Math.abs(amount);
                        dailyData[date].basicServiceFee += fee;
                        totalBasicFee += fee;
                    } else if (type.includes('支出') && remark.includes('闲鱼币智能软件服务费')) {
                        const fee = Math.abs(amount);
                        dailyData[date].smartServiceFee += fee;
                        totalSmartFee += fee;
                    } else if (type.includes('支出') && remark.includes('闲鱼超级擦亮')) {
                        const fee = Math.abs(amount);
                        dailyData[date].superBrightFee += fee;
                        totalSuperBrightFee += fee;
                    } else if (category === '生意' && type.includes('收入')) {
                        dailyData[date].businessIncome += amount;
                        totalBusinessIncome += amount;
                    }
                    
                    // 计算净收益：生意收入 - 基础软件服务费 - 闲鱼币智能软件服务费 - 闲鱼超级擦亮
                    dailyData[date].netEarnings = dailyData[date].businessIncome - 
                                                  dailyData[date].basicServiceFee - 
                                                  dailyData[date].smartServiceFee - 
                                                  dailyData[date].superBrightFee;
                });
                
                const totalNetEarnings = totalBusinessIncome - totalBasicFee - totalSmartFee - totalSuperBrightFee;
                
                console.log('闲鱼收益汇总:', {
                    totalBasicFee,
                    totalSmartFee,
                    totalSuperBrightFee,
                    totalBusinessIncome,
                    totalNetEarnings
                });
                
                // 更新概览卡片
                document.getElementById('basicServiceFee').textContent = '¥' + totalBasicFee.toFixed(2);
                document.getElementById('smartServiceFee').textContent = '¥' + totalSmartFee.toFixed(2);
                document.getElementById('superBrightFee').textContent = '¥' + totalSuperBrightFee.toFixed(2);
                document.getElementById('businessIncome').textContent = '¥' + totalBusinessIncome.toFixed(2);
                document.getElementById('netEarnings').textContent = '¥' + totalNetEarnings.toFixed(2);
                
                // 初始化折线图
                initXianyuEarningsChart(dailyData);
                
                // 填充明细表格
                fillXianyuTable(dailyData);
            }
            
            // 初始化闲鱼收益折线图
            function initXianyuEarningsChart(dailyData) {
                const chartDom = document.getElementById('xianyuEarningsChart');
                const myChart = echarts.init(chartDom);
                
                if (!dailyData || Object.keys(dailyData).length === 0) {
                    myChart.setOption({
                        title: {
                            text: '暂无闲鱼收益数据',
                            left: 'center',
                            top: 'center',
                            textStyle: {
                                color: '#999',
                                fontSize: 16
                            }
                        }
                    });
                    return;
                }
                
                // 按日期排序
                const sortedDates = Object.keys(dailyData).sort();
                
                // 准备数据
                const basicFeeData = sortedDates.map(date => dailyData[date].basicServiceFee);
                const smartFeeData = sortedDates.map(date => dailyData[date].smartServiceFee);
                const superBrightFeeData = sortedDates.map(date => dailyData[date].superBrightFee);
                const businessIncomeData = sortedDates.map(date => dailyData[date].businessIncome);
                const netEarningsData = sortedDates.map(date => dailyData[date].netEarnings);
                
                const option = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross',
                            label: {
                                backgroundColor: '#6a7985'
                            }
                        },
                        formatter: function(params) {
                            let result = params[0].axisValue + '<br/>';
                            params.forEach(param => {
                                result += `${param.seriesName}: ¥${param.value.toFixed(2)}<br/>`;
                            });
                            return result;
                        }
                    },
                    legend: {
                        data: ['基础软件服务费', '闲鱼币智能软件服务费', '闲鱼超级擦亮', '生意收入', '净收益'],
                        top: '5%'
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '10%',
                        top: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: false,
                        data: sortedDates,
                        axisLabel: {
                            formatter: function(value) {
                                return new Date(value).toLocaleDateString('zh-CN', {
                                    month: 'short',
                                    day: 'numeric'
                                });
                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: {
                            formatter: function(value) {
                                return '¥' + value.toFixed(0);
                            }
                        }
                    },
                    series: [
                        {
                            name: '基础软件服务费',
                            type: 'line',
                            data: basicFeeData,
                            itemStyle: {
                                color: '#dc3545'
                            },
                            lineStyle: {
                                width: 2
                            },
                            symbol: 'circle',
                            symbolSize: 6
                        },
                        {
                            name: '闲鱼币智能软件服务费',
                            type: 'line',
                            data: smartFeeData,
                            itemStyle: {
                                color: '#ffc107'
                            },
                            lineStyle: {
                                width: 2
                            },
                            symbol: 'circle',
                            symbolSize: 6
                        },
                        {
                            name: '闲鱼超级擦亮',
                            type: 'line',
                            data: superBrightFeeData,
                            itemStyle: {
                                color: '#ff6a00'
                            },
                            lineStyle: {
                                width: 2
                            },
                            symbol: 'circle',
                            symbolSize: 6
                        },
                        {
                            name: '生意收入',
                            type: 'line',
                            data: businessIncomeData,
                            itemStyle: {
                                color: '#28a745'
                            },
                            lineStyle: {
                                width: 3
                            },
                            symbol: 'circle',
                            symbolSize: 8
                        },
                        {
                            name: '净收益',
                            type: 'line',
                            data: netEarningsData,
                            itemStyle: {
                                color: '#007bff'
                            },
                            lineStyle: {
                                width: 3,
                                type: 'dashed'
                            },
                            symbol: 'diamond',
                            symbolSize: 8,
                            markLine: {
                                data: [
                                    {
                                        type: 'average',
                                        name: '平均值',
                                        label: {
                                            formatter: '平均: ¥{c}'
                                        }
                                    }
                                ]
                            }
                        }
                    ]
                };
                
                myChart.setOption(option);
                
                // 响应窗口大小变化
                window.addEventListener('resize', function() {
                    myChart.resize();
                });
            }
            
            // 填充闲鱼收益明细表格
            function fillXianyuTable(dailyData) {
                const tableBody = document.getElementById('xianyuTableBody');
                if (!tableBody) return;
                
                tableBody.innerHTML = '';
                
                // 按日期排序
                const sortedDates = Object.keys(dailyData).sort().reverse(); // 降序显示，最新的在前面
                
                sortedDates.forEach(date => {
                    const data = dailyData[date];
                    const tr = document.createElement('tr');
                    
                    // 根据净收益设置行样式
                    if (data.netEarnings > 0) {
                        tr.classList.add('table-success');
                    } else if (data.netEarnings < 0) {
                        tr.classList.add('table-danger');
                    }
                    
                    const formattedDate = new Date(date).toLocaleDateString('zh-CN');
                    
                    tr.innerHTML = `
                        <td>${formattedDate}</td>
                        <td class="text-danger">¥${data.basicServiceFee.toFixed(2)}</td>
                        <td class="text-warning">¥${data.smartServiceFee.toFixed(2)}</td>
                        <td style="color: #ff6a00;">¥${data.superBrightFee.toFixed(2)}</td>
                        <td class="text-success">¥${data.businessIncome.toFixed(2)}</td>
                        <td class="fw-bold ${data.netEarnings >= 0 ? 'text-success' : 'text-danger'}">
                            ¥${data.netEarnings.toFixed(2)}
                        </td>
                    `;
                    
                    tableBody.appendChild(tr);
                });
                
                // 如果没有数据，显示提示
                if (sortedDates.length === 0) {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `
                        <td colspan="6" class="text-center text-muted">
                            暂无闲鱼收益数据
                        </td>
                    `;
                    tableBody.appendChild(tr);
                }
            }

            // 初始化概览页面图表
            function initializeOverviewCharts(summaryData) {
                console.log('初始化概览页面图表...');
                
                // 使用延迟加载确保DOM已渲染
                setTimeout(() => {
                    try {
                        initOverviewTrendChart(summaryData);
                        initOverviewMonthlyChart(summaryData);
                        initOverviewCategoryChart(summaryData);
                        initOverviewDailyAverageChart(summaryData);
                        initOverviewCumulativeChart(summaryData);
                        initOverviewAccountChart(summaryData);
                        console.log('概览页面图表初始化完成');
                    } catch (error) {
                        console.error('概览图表初始化错误:', error);
                    }
                }, 200);
            }

            // 1. 收支趋势折线图
            function initOverviewTrendChart(summaryData) {
                const chartDom = document.getElementById('overviewTrendChart');
                if (!chartDom) return;
                
                const myChart = echarts.init(chartDom);
                
                if (!summaryData.daily_trend || Object.keys(summaryData.daily_trend).length === 0) {
                    myChart.setOption({
                        title: {
                            text: '暂无趋势数据',
                            left: 'center',
                            top: 'center',
                            textStyle: { color: '#999', fontSize: 14 }
                        }
                    });
                    return;
                }
                
                const dates = Object.keys(summaryData.daily_trend).sort();
                const values = dates.map(date => summaryData.daily_trend[date]);
                
                const option = {
                    tooltip: {
                        trigger: 'axis',
                        formatter: function(params) {
                            return `${params[0].axisValue}<br/>净收支: ¥${params[0].data.toFixed(2)}`;
                        }
                    },
                    xAxis: {
                        type: 'category',
                        data: dates.map(date => new Date(date).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })),
                        axisLabel: { fontSize: 11 }
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: {
                            formatter: '¥{value}',
                            fontSize: 11
                        }
                    },
                    series: [{
                        data: values,
                        type: 'line',
                        smooth: true,
                        lineStyle: { width: 3 },
                        itemStyle: { color: '#007bff' },
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0, y: 0, x2: 0, y2: 1,
                                colorStops: [
                                    { offset: 0, color: 'rgba(0, 123, 255, 0.3)' },
                                    { offset: 1, color: 'rgba(0, 123, 255, 0.1)' }
                                ]
                            }
                        }
                    }]
                };
                
                myChart.setOption(option);
                window.addEventListener('resize', () => myChart.resize());
            }

            // 2. 月度收支对比柱状图
            function initOverviewMonthlyChart(summaryData) {
                const chartDom = document.getElementById('overviewMonthlyChart');
                if (!chartDom) return;
                
                const myChart = echarts.init(chartDom);
                
                if (!summaryData.monthly_summary || Object.keys(summaryData.monthly_summary).length === 0) {
                    myChart.setOption({
                        title: {
                            text: '暂无月度数据',
                            left: 'center',
                            top: 'center',
                            textStyle: { color: '#999', fontSize: 14 }
                        }
                    });
                    return;
                }
                
                const months = Object.keys(summaryData.monthly_summary).sort();
                const incomeData = months.map(month => summaryData.monthly_summary[month].income || 0);
                const expenseData = months.map(month => Math.abs(summaryData.monthly_summary[month].expense || 0));
                
                const option = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: { type: 'shadow' }
                    },
                    legend: {
                        data: ['收入', '支出'],
                        bottom: 0
                    },
                    xAxis: {
                        type: 'category',
                        data: months,
                        axisLabel: { fontSize: 11 }
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: { formatter: '¥{value}', fontSize: 11 }
                    },
                    series: [
                        {
                            name: '收入',
                            type: 'bar',
                            data: incomeData,
                            itemStyle: { color: '#28a745' }
                        },
                        {
                            name: '支出',
                            type: 'bar',
                            data: expenseData,
                            itemStyle: { color: '#dc3545' }
                        }
                    ]
                };
                
                myChart.setOption(option);
                window.addEventListener('resize', () => myChart.resize());
            }

            // 3. 分类支出柱状图
            function initOverviewCategoryChart(summaryData) {
                const chartDom = document.getElementById('overviewCategoryChart');
                if (!chartDom) return;
                
                const myChart = echarts.init(chartDom);
                
                if (!summaryData.category_summary || Object.keys(summaryData.category_summary).length === 0) {
                    myChart.setOption({
                        title: {
                            text: '暂无分类数据',
                            left: 'center',
                            top: 'center',
                            textStyle: { color: '#999', fontSize: 14 }
                        }
                    });
                    return;
                }
                
                // 取前6个最大的支出分类
                const sortedCategories = Object.entries(summaryData.category_summary)
                    .filter(([_, amount]) => parseFloat(amount) < 0) // 只要支出
                    .sort((a, b) => Math.abs(b[1]) - Math.abs(a[1]))
                    .slice(0, 6);
                
                if (sortedCategories.length === 0) {
                    myChart.setOption({
                        title: {
                            text: '暂无支出分类数据',
                            left: 'center',
                            top: 'center',
                            textStyle: { color: '#999', fontSize: 14 }
                        }
                    });
                    return;
                }
                
                const categories = sortedCategories.map(([category, _]) => category);
                const values = sortedCategories.map(([_, amount]) => Math.abs(amount));
                
                const option = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: { type: 'shadow' },
                        formatter: function(params) {
                            return `${params[0].name}<br/>支出: ¥${params[0].data.toFixed(2)}`;
                        }
                    },
                    xAxis: {
                        type: 'category',
                        data: categories,
                        axisLabel: { 
                            rotate: 45,
                            fontSize: 11,
                            formatter: function(value) {
                                return value.length > 4 ? value.substring(0, 4) + '...' : value;
                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: { formatter: '¥{value}', fontSize: 11 }
                    },
                    series: [{
                        type: 'bar',
                        data: values,
                        itemStyle: {
                            color: {
                                type: 'linear',
                                x: 0, y: 1, x2: 0, y2: 0,
                                colorStops: [
                                    { offset: 0, color: '#FF7043' },
                                    { offset: 1, color: '#FFAB91' }
                                ]
                            }
                        },
                        barWidth: '60%'
                    }]
                };
                
                myChart.setOption(option);
                window.addEventListener('resize', () => myChart.resize());
            }

            // 4. 日均收支对比雷达图
            function initOverviewDailyAverageChart(summaryData) {
                const chartDom = document.getElementById('overviewDailyAverageChart');
                if (!chartDom) return;
                
                const myChart = echarts.init(chartDom);
                
                // 计算日均数据
                const totalDays = allData && allData.length > 0 ? 
                    new Set(allData.map(item => new Date(item.记录时间).toDateString())).size : 1;
                
                const avgIncome = summaryData.total_income / totalDays;
                const avgExpense = Math.abs(summaryData.total_expense) / totalDays;
                const avgTransactions = (allData ? allData.length : 0) / totalDays;
                
                // 获取最活跃分类的平均值
                const categoryAvg = summaryData.category_summary ? 
                    Object.values(summaryData.category_summary).reduce((sum, val) => sum + Math.abs(val), 0) / Object.keys(summaryData.category_summary).length / totalDays : 0;
                
                const option = {
                    tooltip: {},
                    radar: {
                        indicator: [
                            { name: '日均收入', max: Math.max(avgIncome * 2, 100) },
                            { name: '日均支出', max: Math.max(avgExpense * 2, 100) },
                            { name: '日均交易', max: Math.max(avgTransactions * 2, 10) },
                            { name: '分类活跃度', max: Math.max(categoryAvg * 2, 50) },
                            { name: '资金流动', max: Math.max((avgIncome + avgExpense), 100) }
                        ],
                        radius: '70%'
                    },
                    series: [{
                        name: '日均分析',
                        type: 'radar',
                        data: [{
                            value: [avgIncome, avgExpense, avgTransactions, categoryAvg, (avgIncome + avgExpense) / 2],
                            name: '日均数据',
                            itemStyle: { color: '#4ECDC4' },
                            areaStyle: { color: 'rgba(78, 205, 196, 0.3)' }
                        }]
                    }]
                };
                
                myChart.setOption(option);
                window.addEventListener('resize', () => myChart.resize());
            }

            // 5. 累计金额变化折线图
            function initOverviewCumulativeChart(summaryData) {
                const chartDom = document.getElementById('overviewCumulativeChart');
                if (!chartDom) return;
                
                const myChart = echarts.init(chartDom);
                
                if (!summaryData.cumulative_trend || Object.keys(summaryData.cumulative_trend).length === 0) {
                    myChart.setOption({
                        title: {
                            text: '暂无累计数据',
                            left: 'center',
                            top: 'center',
                            textStyle: { color: '#999', fontSize: 14 }
                        }
                    });
                    return;
                }
                
                const dates = Object.keys(summaryData.cumulative_trend).sort();
                const values = dates.map(date => summaryData.cumulative_trend[date]);
                
                const option = {
                    tooltip: {
                        trigger: 'axis',
                        formatter: function(params) {
                            return `${params[0].axisValue}<br/>累计: ¥${params[0].data.toFixed(2)}`;
                        }
                    },
                    xAxis: {
                        type: 'category',
                        data: dates.map(date => new Date(date).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })),
                        axisLabel: { fontSize: 11 }
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: { formatter: '¥{value}', fontSize: 11 }
                    },
                    series: [{
                        data: values,
                        type: 'line',
                        smooth: true,
                        lineStyle: { width: 3 },
                        itemStyle: { color: '#20c997' },
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0, y: 0, x2: 0, y2: 1,
                                colorStops: [
                                    { offset: 0, color: 'rgba(32, 201, 151, 0.3)' },
                                    { offset: 1, color: 'rgba(32, 201, 151, 0.1)' }
                                ]
                            }
                        },
                        markLine: {
                            data: [
                                { type: 'average', name: '平均值' }
                            ]
                        }
                    }]
                };
                
                myChart.setOption(option);
                window.addEventListener('resize', () => myChart.resize());
            }

            // 6. 账户资金分布饼图
            function initOverviewAccountChart(summaryData) {
                const chartDom = document.getElementById('overviewAccountChart');
                if (!chartDom) return;
                
                const myChart = echarts.init(chartDom);
                
                if (!summaryData.account_summary || Object.keys(summaryData.account_summary).length === 0) {
                    myChart.setOption({
                        title: {
                            text: '暂无账户数据',
                            left: 'center',
                            top: 'center',
                            textStyle: { color: '#999', fontSize: 14 }
                        }
                    });
                    return;
                }
                
                const seriesData = Object.entries(summaryData.account_summary)
                    .filter(([_, amount]) => Math.abs(amount) > 0.01)
                    .map(([account, amount]) => ({
                        name: account || '未知账户',
                        value: Math.abs(amount)
                    }))
                    .sort((a, b) => b.value - a.value);
                
                if (seriesData.length === 0) {
                    myChart.setOption({
                        title: {
                            text: '暂无账户资金数据',
                            left: 'center',
                            top: 'center',
                            textStyle: { color: '#999', fontSize: 14 }
                        }
                    });
                    return;
                }
                
                const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'];
                
                const option = {
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b}: ¥{c} ({d}%)'
                    },
                    legend: {
                        orient: 'vertical',
                        right: '5%',
                        top: 'center',
                        data: seriesData.map(item => item.name),
                        textStyle: { fontSize: 11 }
                    },
                    series: [{
                        name: '账户分布',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        center: ['40%', '50%'],
                        data: seriesData,
                        color: colors,
                        itemStyle: {
                            borderRadius: 8,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            formatter: '{b}\n{d}%',
                            fontSize: 11
                        },
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }]
                };
                
                myChart.setOption(option);
                window.addEventListener('resize', () => myChart.resize());
            }
        });
    </script>

    <!-- 个人中心模态框 -->
    <div class="custom-modal" id="profileModal">
        <div class="modal-content-custom">
            <div class="modal-header-custom">
                <button class="modal-close-custom" onclick="closeCustomModal('profileModal')">
                    <i class="bi bi-x"></i>
                </button>
                <h2 class="modal-title-custom">
                    <i class="bi bi-person-circle"></i>
                    个人中心
                </h2>
            </div>
            <div class="modal-body-custom">
                <div class="alert-custom alert-success-custom" id="profileSuccessAlert" style="display: none;"></div>
                <div class="alert-custom alert-danger-custom" id="profileErrorAlert" style="display: none;"></div>
                
                <div class="profile-upload-area">
                    <img src="{{ url_for('get_avatar', user_id=session.get('user_id', 'default')) }}" 
                         alt="用户头像" class="current-avatar" id="currentAvatar">
                    <div>
                        <input type="file" id="avatarInput" accept="image/*" style="display: none;">
                        <button class="upload-btn" onclick="document.getElementById('avatarInput').click()">
                            <i class="bi bi-camera me-2"></i>更换头像
                        </button>
                    </div>
                </div>

                <div class="form-group-custom">
                    <label class="form-label-custom">用户名</label>
                    <input type="text" class="form-control-custom" id="profileUsername" 
                           value="{{ session.get('username', '') }}" readonly>
                </div>

                <div class="form-group-custom">
                    <label class="form-label-custom">邮箱地址</label>
                    <input type="email" class="form-control-custom" id="profileEmail" 
                           value="{{ session.get('email', '') }}" readonly>
                </div>

                <div class="form-group-custom">
                    <label class="form-label-custom">注册时间</label>
                    <input type="text" class="form-control-custom" id="profileCreateTime" 
                           value="加载中..." readonly>
                </div>
            </div>
        </div>
    </div>

    <!-- 账户安全模态框 -->
    <div class="custom-modal" id="securityModal">
        <div class="modal-content-custom">
            <div class="modal-header-custom">
                <button class="modal-close-custom" onclick="closeCustomModal('securityModal')">
                    <i class="bi bi-x"></i>
                </button>
                <h2 class="modal-title-custom">
                    <i class="bi bi-shield-lock"></i>
                    账户安全
                </h2>
            </div>
            <div class="modal-body-custom">
                <div class="alert-custom alert-success-custom" id="securitySuccessAlert" style="display: none;"></div>
                <div class="alert-custom alert-danger-custom" id="securityErrorAlert" style="display: none;"></div>
                
                <!-- 修改密码表单 -->
                <h5 class="mb-3">修改密码</h5>
                <form id="changePasswordForm">
                    <div class="form-group-custom">
                        <label class="form-label-custom">邮箱验证码</label>
                        <div style="display: flex; gap: 10px;">
                            <input type="text" class="form-control-custom" id="passwordVerificationCode" 
                                   placeholder="请输入验证码" required style="flex: 1;">
                            <button type="button" class="upload-btn" id="sendPasswordCodeBtn" style="white-space: nowrap;">
                                获取验证码
                            </button>
                        </div>
                        <small style="color: #6c757d; font-size: 0.85rem; display: block; margin-top: 5px;">
                            验证码将发送到您的邮箱：{{ session.get('email', '') }}
                        </small>
                    </div>

                    <div class="form-group-custom">
                        <label class="form-label-custom">新密码</label>
                        <input type="password" class="form-control-custom" id="newPassword" 
                               placeholder="请输入新密码（至少6位）" required>
                    </div>

                    <div class="form-group-custom">
                        <label class="form-label-custom">确认新密码</label>
                        <input type="password" class="form-control-custom" id="confirmNewPassword" 
                               placeholder="请再次输入新密码" required>
                    </div>

                    <button type="submit" class="btn-primary-custom">
                        <i class="bi bi-lock me-2"></i>修改密码
                    </button>
                </form>

                <hr class="my-4">

                <!-- 修改邮箱表单 -->
                <h5 class="mb-3">修改邮箱</h5>
                <form id="changeEmailForm">
                    <div class="form-group-custom">
                        <label class="form-label-custom">新邮箱地址</label>
                        <input type="email" class="form-control-custom" id="newEmail" 
                               placeholder="请输入新的邮箱地址" required>
                    </div>

                    <div class="form-group-custom">
                        <label class="form-label-custom">邮箱验证码</label>
                        <div style="display: flex; gap: 10px;">
                            <input type="text" class="form-control-custom" id="emailVerificationCode" 
                                   placeholder="请输入验证码" required style="flex: 1;">
                            <button type="button" class="upload-btn" id="sendEmailCodeBtn" style="white-space: nowrap;">
                                获取验证码
                            </button>
                        </div>
                    </div>

                    <button type="submit" class="btn-primary-custom">
                        <i class="bi bi-envelope me-2"></i>修改邮箱
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- 设置模态框 -->
    <div class="custom-modal" id="settingsModal">
        <div class="modal-content-custom">
            <div class="modal-header-custom">
                <button class="modal-close-custom" onclick="closeCustomModal('settingsModal')">
                    <i class="bi bi-x"></i>
                </button>
                <h2 class="modal-title-custom">
                    <i class="bi bi-gear"></i>
                    系统设置
                </h2>
            </div>
            <div class="modal-body-custom">
                <div class="alert-custom alert-success-custom" id="settingsSuccessAlert" style="display: none;"></div>
                <div class="alert-custom alert-danger-custom" id="settingsErrorAlert" style="display: none;"></div>
                
                <!-- 数据管理 -->
                <h5 class="mb-3">数据管理</h5>
                <div class="form-group-custom">
                    <label class="form-label-custom">导出数据</label>
                    <p class="text-muted" style="font-size: 0.9rem; margin-bottom: 10px;">
                        导出您的所有账单数据为CSV格式文件
                    </p>
                    <button class="btn-primary-custom" onclick="exportUserData()">
                        <i class="bi bi-download me-2"></i>导出我的数据
                    </button>
                </div>

                <hr class="my-4">

                <!-- 清空数据 -->
                <h5 class="mb-3">危险操作</h5>
                <div class="form-group-custom">
                    <label class="form-label-custom">清空账单数据</label>
                    <p class="text-muted" style="font-size: 0.9rem; margin-bottom: 10px;">
                        此操作将永久删除您的所有账单数据，无法恢复
                    </p>
                    <button class="btn-primary-custom" onclick="confirmClearData()" 
                            style="background: linear-gradient(135deg, #dc3545, #c82333);">
                        <i class="bi bi-trash me-2"></i>清空所有数据
                    </button>
                </div>

                <hr class="my-4">

                <!-- 账户操作 -->
                <h5 class="mb-3">账户操作</h5>
                <div class="form-group-custom">
                    <label class="form-label-custom">注销账户</label>
                    <p class="text-muted" style="font-size: 0.9rem; margin-bottom: 10px;">
                        此操作将永久删除您的账户和所有相关数据，无法恢复
                    </p>
                    <button class="btn-primary-custom" onclick="confirmDeleteAccount()" 
                            style="background: linear-gradient(135deg, #dc3545, #c82333);">
                        <i class="bi bi-person-x me-2"></i>注销账户
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化用户信息
        function initUserProfile() {
            // 获取用户信息
            fetch('/api/user_info')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        showPageAlert('profileErrorMessage', data.error);
                    } else {
                        document.getElementById('profileUsernameDisplay').value = data.username;
                        document.getElementById('profileEmailDisplay').value = data.email;
                        
                        // 格式化注册时间
                        if (data.created_at) {
                            const createTime = new Date(data.created_at);
                            const formattedTime = createTime.toLocaleString('zh-CN', {
                                year: 'numeric',
                                month: '2-digit',
                                day: '2-digit',
                                hour: '2-digit',
                                minute: '2-digit',
                                second: '2-digit'
                            });
                            document.getElementById('profileCreateTimeDisplay').value = formattedTime;
                        }
                    }
                })
                .catch(error => {
                    console.error('获取用户信息错误:', error);
                    showPageAlert('profileErrorMessage', '获取用户信息失败');
                });
        }
        
        // 用户名编辑功能
        let isEditingUsername = false;
        let originalUsername = '';
        
        document.addEventListener('DOMContentLoaded', function() {
            // 编辑用户名按钮
            const editUsernameBtn = document.getElementById('editUsernameBtn');
            const saveUsernameBtn = document.getElementById('saveUsernameBtn');
            const cancelUsernameBtn = document.getElementById('cancelUsernameBtn');
            const usernameInput = document.getElementById('profileUsernameDisplay');
            
            if (editUsernameBtn) {
                editUsernameBtn.addEventListener('click', function() {
                    if (!isEditingUsername) {
                        // 进入编辑模式
                        isEditingUsername = true;
                        originalUsername = usernameInput.value;
                        
                        usernameInput.removeAttribute('readonly');
                        usernameInput.focus();
                        editUsernameBtn.style.display = 'none';
                        saveUsernameBtn.style.display = 'inline-block';
                        cancelUsernameBtn.style.display = 'inline-block';
                    }
                });
            }
            
            if (saveUsernameBtn) {
                saveUsernameBtn.addEventListener('click', function() {
                    const newUsername = usernameInput.value.trim();
                    
                    if (!newUsername) {
                        showPageAlert('profileErrorMessage', '用户名不能为空');
                        return;
                    }
                    
                    if (newUsername.length < 2) {
                        showPageAlert('profileErrorMessage', '用户名至少需要2个字符');
                        return;
                    }
                    
                    if (newUsername === originalUsername) {
                        // 没有改变，直接退出编辑模式
                        exitUsernameEditMode();
                        return;
                    }
                    
                    // 保存新用户名
                    fetch('/change_username', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            new_username: newUsername
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.error) {
                            showPageAlert('profileErrorMessage', data.error);
                        } else {
                            showPageAlert('profileSuccessMessage', data.message);
                            // 更新导航栏中的用户名显示
                            const userNameElements = document.querySelectorAll('.user-name, .dropdown-username');
                            userNameElements.forEach(el => {
                                if (el) el.textContent = data.new_username;
                            });
                            exitUsernameEditMode();
                        }
                    })
                    .catch(error => {
                        console.error('修改用户名错误:', error);
                        showPageAlert('profileErrorMessage', '修改失败，请重试');
                    });
                });
            }
            
            if (cancelUsernameBtn) {
                cancelUsernameBtn.addEventListener('click', function() {
                    // 恢复原始值
                    usernameInput.value = originalUsername;
                    exitUsernameEditMode();
                });
            }
            
            // 监听 Enter 键保存，Esc 键取消
            if (usernameInput) {
                usernameInput.addEventListener('keydown', function(e) {
                    if (isEditingUsername) {
                        if (e.key === 'Enter') {
                            e.preventDefault();
                            saveUsernameBtn && saveUsernameBtn.click();
                        } else if (e.key === 'Escape') {
                            e.preventDefault();
                            cancelUsernameBtn && cancelUsernameBtn.click();
                        }
                    }
                });
            }
            
            function exitUsernameEditMode() {
                isEditingUsername = false;
                usernameInput.setAttribute('readonly', 'readonly');
                editUsernameBtn.style.display = 'inline-block';
                saveUsernameBtn.style.display = 'none';
                cancelUsernameBtn.style.display = 'none';
            }
        });

        // 用户菜单导航功能
        function showUserProfile() {
            // 隐藏下拉菜单
            hideUserDropdown();
            // 切换到个人中心页面
            switchToSection('user-profile');
            // 初始化用户信息
            setTimeout(() => {
                initUserProfile();
            }, 100);
        }
        
        function showUserSecurity() {
            // 隐藏下拉菜单
            hideUserDropdown();
            // 切换到账户安全页面
            switchToSection('user-security');
        }
        
        function showUserSettings() {
            // 隐藏下拉菜单
            hideUserDropdown();
            // 切换到设置页面
            switchToSection('user-settings');
        }
        
        function switchToSection(targetId) {
            // 移除所有活动状态
            document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
            document.querySelectorAll('.content-section').forEach(s => s.classList.remove('active'));
            
            // 激活目标链接和区域
            const targetLink = document.querySelector(`[data-target="${targetId}"]`);
            const targetSection = document.getElementById(targetId);
            
            if (targetLink) targetLink.classList.add('active');
            if (targetSection) targetSection.classList.add('active');
            
            // 如果是移动设备，自动收起侧边栏
            if (window.innerWidth <= 768) {
                const sidebar = document.getElementById('sidebar');
                const mainContent = document.getElementById('mainContent');
                sidebar.classList.add('collapsed');
                mainContent.classList.add('expanded');
            }
            
            // 滚动到顶部
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // 用户下拉菜单功能
        document.addEventListener('DOMContentLoaded', function() {
            const userDropdownTrigger = document.getElementById('userDropdownTrigger');
            const userDropdownMenu = document.getElementById('userDropdownMenu');

            // 点击用户头像区域显示/隐藏下拉菜单
            userDropdownTrigger.addEventListener('click', function(e) {
                e.stopPropagation();
                const isActive = userDropdownTrigger.classList.contains('active');
                
                if (isActive) {
                    hideUserDropdown();
                } else {
                    showUserDropdown();
                }
            });

            // 点击其他地方隐藏下拉菜单
            document.addEventListener('click', function() {
                hideUserDropdown();
            });

            // 阻止下拉菜单内部点击事件冒泡
            userDropdownMenu.addEventListener('click', function(e) {
                e.stopPropagation();
            });

            function showUserDropdown() {
                userDropdownTrigger.classList.add('active');
                userDropdownMenu.classList.add('show');
            }

            function hideUserDropdown() {
                userDropdownTrigger.classList.remove('active');
                userDropdownMenu.classList.remove('show');
            }
        });

        // 模态框管理函数
        function showProfileModal() {
            document.getElementById('profileModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        function showSecurityModal() {
            document.getElementById('securityModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        function showSettingsModal() {
            document.getElementById('settingsModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        function closeCustomModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            document.body.style.overflow = 'auto';
            hideUserDropdown();
        }

        function hideUserDropdown() {
            const trigger = document.getElementById('userDropdownTrigger');
            const menu = document.getElementById('userDropdownMenu');
            if (trigger) trigger.classList.remove('active');
            if (menu) menu.classList.remove('show');
        }

        // 头像上传功能（模态框版本）
        document.getElementById('avatarInput') && document.getElementById('avatarInput').addEventListener('change', function(e) {
            handleAvatarUpload(e, 'profileErrorAlert', 'profileSuccessAlert', 'currentAvatar');
        });
        
        // 头像上传功能（页面版本）
        document.getElementById('profileAvatarInput') && document.getElementById('profileAvatarInput').addEventListener('change', function(e) {
            handleAvatarUpload(e, 'profileErrorMessage', 'profileSuccessMessage', 'profileAvatar');
        });
        
        function handleAvatarUpload(e, errorAlertId, successAlertId, avatarImageId) {
            const file = e.target.files[0];
            if (file) {
                if (file.size > 5 * 1024 * 1024) {
                    showPageAlert(errorAlertId, '图片大小不能超过5MB');
                    return;
                }

                const formData = new FormData();
                formData.append('avatar', file);

                fetch('/upload_avatar', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        showPageAlert(errorAlertId, data.error);
                    } else {
                        showPageAlert(successAlertId, '头像更新成功');
                        // 更新所有头像显示，添加时间戳避免缓存
                        const timestamp = Date.now();
                        const newAvatarUrl = `/avatar/${data.user_id}?t=${timestamp}`;
                        const avatarImg = document.getElementById(avatarImageId);
                        const userAvatar = document.getElementById('userAvatar');
                        const dropdownAvatar = document.querySelector('.dropdown-avatar');
                        const currentAvatar = document.getElementById('currentAvatar');
                        
                        if (avatarImg) avatarImg.src = newAvatarUrl;
                        if (userAvatar) userAvatar.src = newAvatarUrl;
                        if (dropdownAvatar) dropdownAvatar.src = newAvatarUrl;
                        if (currentAvatar) currentAvatar.src = newAvatarUrl;
                    }
                })
                .catch(error => {
                    showPageAlert(errorAlertId, '上传失败，请重试');
                    console.error('头像上传错误:', error);
                });
            }
        }

        // 发送密码修改验证码（模态框版本）
        document.getElementById('sendPasswordCodeBtn') && document.getElementById('sendPasswordCodeBtn').addEventListener('click', function() {
            handleSendPasswordCode('securityErrorAlert', 'securitySuccessAlert', this);
        });
        
        // 发送密码修改验证码（页面版本）
        document.getElementById('securitySendPasswordCodeBtn') && document.getElementById('securitySendPasswordCodeBtn').addEventListener('click', function() {
            handleSendPasswordCode('securityErrorMessage', 'securitySuccessMessage', this);
        });
        
        function handleSendPasswordCode(errorAlertId, successAlertId, btn) {
            btn.disabled = true;
            btn.textContent = '发送中...';

            fetch('/send_verification_code', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email: '{{ session.get("email", "") }}' })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showPageAlert(errorAlertId, data.error);
                    btn.disabled = false;
                    btn.textContent = '获取验证码';
                } else {
                    showPageAlert(successAlertId, data.message);
                    startPasswordCodeCountdown(btn);
                }
            })
            .catch(error => {
                showPageAlert(errorAlertId, '发送失败，请重试');
                btn.disabled = false;
                btn.textContent = '获取验证码';
                console.error('发送验证码错误:', error);
            });
        }
        
        // 密码修改验证码倒计时
        function startPasswordCodeCountdown(btn) {
            let countdown = 60;
            const interval = setInterval(() => {
                btn.textContent = `${countdown}秒后重试`;
                countdown--;
                if (countdown < 0) {
                    clearInterval(interval);
                    btn.disabled = false;
                    btn.textContent = '获取验证码';
                }
            }, 1000);
        }

        // 修改密码功能（模态框版本）
        document.getElementById('changePasswordForm') && document.getElementById('changePasswordForm').addEventListener('submit', function(e) {
            handlePasswordChange(e, 'passwordVerificationCode', 'newPassword', 'confirmNewPassword', 'securityErrorAlert', 'securitySuccessAlert', 'changePasswordForm');
        });
        
        // 修改密码功能（页面版本）
        document.getElementById('securityChangePasswordForm') && document.getElementById('securityChangePasswordForm').addEventListener('submit', function(e) {
            handlePasswordChange(e, 'securityPasswordVerificationCode', 'securityNewPassword', 'securityConfirmNewPassword', 'securityErrorMessage', 'securitySuccessMessage', 'securityChangePasswordForm');
        });
        
        function handlePasswordChange(e, verificationCodeId, newPwdId, confirmPwdId, errorAlertId, successAlertId, formId) {
            e.preventDefault();
            
            const verificationCode = document.getElementById(verificationCodeId).value;
            const newPassword = document.getElementById(newPwdId).value;
            const confirmNewPassword = document.getElementById(confirmPwdId).value;

            if (!verificationCode) {
                showPageAlert(errorAlertId, '请输入邮箱验证码');
                return;
            }

            if (newPassword.length < 6) {
                showPageAlert(errorAlertId, '新密码长度至少为6位');
                return;
            }

            if (newPassword !== confirmNewPassword) {
                showPageAlert(errorAlertId, '两次输入的新密码不一致');
                return;
            }

            fetch('/change_password', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    verification_code: verificationCode,
                    new_password: newPassword
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showPageAlert(errorAlertId, data.error);
                } else {
                    showPageAlert(successAlertId, data.message);
                    document.getElementById(formId).reset();
                }
            })
            .catch(error => {
                showPageAlert(errorAlertId, '修改失败，请重试');
                console.error('修改密码错误:', error);
            });
        }

        // 发送邮箱验证码（模态框版本）
        document.getElementById('sendEmailCodeBtn') && document.getElementById('sendEmailCodeBtn').addEventListener('click', function() {
            handleSendEmailCode('newEmail', 'securityErrorAlert', 'securitySuccessAlert', this);
        });
        
        // 发送邮箱验证码（页面版本）
        document.getElementById('securitySendEmailCodeBtn') && document.getElementById('securitySendEmailCodeBtn').addEventListener('click', function() {
            handleSendEmailCode('securityNewEmail', 'securityErrorMessage', 'securitySuccessMessage', this);
        });
        
        function handleSendEmailCode(emailInputId, errorAlertId, successAlertId, btn) {
            const email = document.getElementById(emailInputId).value.trim();
            
            if (!email) {
                showPageAlert(errorAlertId, '请先输入新邮箱地址');
                return;
            }

            btn.disabled = true;
            btn.textContent = '发送中...';

            fetch('/send_verification_code', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email: email })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showPageAlert(errorAlertId, data.error);
                    btn.disabled = false;
                    btn.textContent = '获取验证码';
                } else {
                    showPageAlert(successAlertId, data.message);
                    startEmailCountdown(btn);
                }
            })
            .catch(error => {
                showPageAlert(errorAlertId, '发送失败，请重试');
                btn.disabled = false;
                btn.textContent = '获取验证码';
                console.error('发送验证码错误:', error);
            });
        }

        // 邮箱验证码倒计时
        function startEmailCountdown(btn) {
            let countdown = 60;
            const interval = setInterval(() => {
                btn.textContent = `${countdown}秒后重试`;
                countdown--;
                if (countdown < 0) {
                    clearInterval(interval);
                    btn.disabled = false;
                    btn.textContent = '获取验证码';
                }
            }, 1000);
        }

        // 修改邮箱功能（模态框版本）
        document.getElementById('changeEmailForm') && document.getElementById('changeEmailForm').addEventListener('submit', function(e) {
            handleEmailChange(e, 'newEmail', 'emailVerificationCode', 'securityErrorAlert', 'securitySuccessAlert', 'changeEmailForm');
        });
        
        // 修改邮箱功能（页面版本）
        document.getElementById('securityChangeEmailForm') && document.getElementById('securityChangeEmailForm').addEventListener('submit', function(e) {
            handleEmailChange(e, 'securityNewEmail', 'securityEmailVerificationCode', 'securityErrorMessage', 'securitySuccessMessage', 'securityChangeEmailForm');
        });
        
        function handleEmailChange(e, emailInputId, codeInputId, errorAlertId, successAlertId, formId) {
            e.preventDefault();
            
            const newEmail = document.getElementById(emailInputId).value.trim();
            const verificationCode = document.getElementById(codeInputId).value.trim();

            fetch('/change_email', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    new_email: newEmail,
                    verification_code: verificationCode
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showPageAlert(errorAlertId, data.error);
                } else {
                    showPageAlert(successAlertId, data.message);
                    document.getElementById(formId).reset();
                }
            })
            .catch(error => {
                showPageAlert(errorAlertId, '修改失败，请重试');
                console.error('修改邮箱错误:', error);
            });
        }

        // 导出数据功能（模态框版本）
        function exportUserData() {
            handleExportUserData('settingsSuccessAlert', 'settingsErrorAlert');
        }
        
        // 导出数据功能（页面版本）
        function exportUserDataInPage() {
            handleExportUserData('settingsSuccessMessage', 'settingsErrorMessage');
        }
        
        function handleExportUserData(successAlertId, errorAlertId) {
            fetch('/export_user_data', {
                method: 'GET'
            })
            .then(response => {
                if (response.ok) {
                    return response.blob();
                } else {
                    throw new Error('导出失败');
                }
            })
            .then(blob => {
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `账单数据_${new Date().toISOString().split('T')[0]}.csv`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                showPageAlert(successAlertId, '数据导出成功');
            })
            .catch(error => {
                showPageAlert(errorAlertId, '导出失败，请重试');
                console.error('导出数据错误:', error);
            });
        }

        // 确认清空数据（模态框版本）
        function confirmClearData() {
            if (confirm('确定要清空所有账单数据吗？此操作无法恢复！')) {
                if (confirm('请再次确认：这将永久删除您的所有账单数据！')) {
                    clearUserData('settingsSuccessAlert', 'settingsErrorAlert');
                }
            }
        }
        
        // 确认清空数据（页面版本）
        function confirmClearDataInPage() {
            if (confirm('确定要清空所有账单数据吗？此操作无法恢复！')) {
                if (confirm('请再次确认：这将永久删除您的所有账单数据！')) {
                    clearUserData('settingsSuccessMessage', 'settingsErrorMessage');
                }
            }
        }

        // 清空用户数据
        function clearUserData(successAlertId, errorAlertId) {
            fetch('/clear_user_data', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showPageAlert(errorAlertId, data.error);
                } else {
                    showPageAlert(successAlertId, data.message);
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 2000);
                }
            })
            .catch(error => {
                showPageAlert(errorAlertId, '操作失败，请重试');
                console.error('清空数据错误:', error);
            });
        }

        // 确认注销账户（模态框版本）
        function confirmDeleteAccount() {
            if (confirm('确定要注销账户吗？此操作无法恢复！')) {
                if (confirm('请再次确认：这将永久删除您的账户和所有数据！')) {
                    deleteAccount('settingsErrorAlert');
                }
            }  
        }
        
        // 确认注销账户（页面版本）
        function confirmDeleteAccountInPage() {
            if (confirm('确定要注销账户吗？此操作无法恢复！')) {
                if (confirm('请再次确认：这将永久删除您的账户和所有数据！')) {
                    deleteAccount('settingsErrorMessage');
                }
            }  
        }

        // 注销账户
        function deleteAccount(errorAlertId) {
            fetch('/delete_account', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showPageAlert(errorAlertId, data.error);
                } else {
                    alert('账户注销成功');
                    window.location.href = '/';
                }
            })
            .catch(error => {
                showPageAlert(errorAlertId, '操作失败，请重试');
                console.error('注销账户错误:', error);
            });
        }

        // 自定义提示消息函数（模态框版本）
        function showCustomAlert(alertId, message) {
            hideCustomAlerts();
            const alertElement = document.getElementById(alertId);
            if (alertElement) {
                alertElement.textContent = message;
                alertElement.style.display = 'block';
                setTimeout(() => {
                    alertElement.style.display = 'none';
                }, 5000);
            }
        }

        function hideCustomAlerts() {
            const alerts = document.querySelectorAll('.alert-custom');
            alerts.forEach(alert => {
                alert.style.display = 'none';
            });
        }
        
        // 页面提示消息函数
        function showPageAlert(alertId, message) {
            hidePageAlerts();
            const alertElement = document.getElementById(alertId);
            if (alertElement) {
                alertElement.textContent = message;
                alertElement.style.display = 'block';
                setTimeout(() => {
                    alertElement.style.display = 'none';
                }, 5000);
            }
        }

        function hidePageAlerts() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                if (alert.id.includes('Message')) {
                    alert.style.display = 'none';
                }
            });
        }

        // 点击模态框背景关闭
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('custom-modal')) {
                closeCustomModal(e.target.id);
            }
        });

        // 在图表初始化器中添加上传记录的处理已经在上面的chartInitializers对象中定义
    </script>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/sidebar.js') }}"></script>
</body>
</html>
