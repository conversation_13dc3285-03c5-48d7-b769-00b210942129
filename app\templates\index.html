<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账单分析系统 - 数据上传</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <style>
        :root {
            --primary-color: #4361ee;
            --primary-light: rgba(67, 97, 238, 0.1);
            --secondary-color: #3f37c9;
            --accent-color: #4cc9f0;
            --success-color: #4ade80;
            --danger-color: #f87171;
            --bg-color: #f8fafc;
            --card-bg: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --border-color: #cbd5e1;
        }

        body {
            background-color: var(--bg-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow-x: hidden;
        }

        .bg-decoration {
            position: absolute;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(76, 201, 240, 0.15) 0%, rgba(67, 97, 238, 0.08) 100%);
            z-index: -1;
            filter: blur(25px);
        }

        .bg-decoration-1 {
            top: -100px;
            left: -150px;
            width: 450px;
            height: 450px;
        }

        .bg-decoration-2 {
            bottom: -150px;
            right: -100px;
            width: 500px;
            height: 500px;
            background: radial-gradient(circle, rgba(76, 201, 240, 0.1) 0%, rgba(74, 222, 128, 0.08) 100%);
        }

        .bg-decoration-3 {
            top: 40%;
            right: 25%;
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(74, 222, 128, 0.12) 0%, rgba(76, 201, 240, 0.06) 100%);
        }

        .container {
            max-width: 1000px;
            padding: 30px;
        }

        .app-card {
            background-color: var(--card-bg);
            border-radius: 24px;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.4s ease;
            position: relative;
        }

        .app-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 60px rgba(67, 97, 238, 0.15);
        }

        .card-header-bg {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            height: 140px;
            position: relative;
            z-index: 1;
            overflow: hidden;
        }

        .card-header-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            opacity: 0.2;
        }

        .card-header-bg::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 50%;
            background: linear-gradient(to top, var(--card-bg), transparent);
            z-index: -1;
        }

        .card-body {
            position: relative;
            margin-top: -60px;
            z-index: 2;
            padding: 35px;
        }

        .app-title {
            font-weight: 800;
            color: var(--text-primary);
            margin-bottom: 5px;
            letter-spacing: -0.5px;
        }

        .app-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
            margin-bottom: 30px;
        }

        .upload-zone {
            border: 2px dashed var(--border-color);
            border-radius: 16px;
            padding: 40px 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            margin-bottom: 30px;
            background-color: #f8fafc;
            position: relative;
            overflow: hidden;
        }

        .upload-zone:hover, .upload-zone.drag-over {
            border-color: var(--primary-color);
            background-color: var(--primary-light);
            transform: scale(1.01);
        }

        .upload-zone:hover .upload-icon, .upload-zone.drag-over .upload-icon {
            transform: scale(1.1) translateY(-5px);
        }

        .upload-icon {
            font-size: 3.5rem;
            color: var(--primary-color);
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .upload-text {
            color: var(--text-primary);
            font-weight: 600;
            margin-bottom: 15px;
        }

        .file-types {
            font-size: 0.9rem;
            color: var(--text-muted);
            position: relative;
            display: inline-block;
        }

        .file-types::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 3px;
            background-color: var(--accent-color);
            border-radius: 3px;
        }

        .file-input {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            opacity: 0;
            cursor: pointer;
        }

        .file-details {
            background-color: #f1f5f9;
            border-radius: 16px;
            padding: 18px 25px;
            margin-bottom: 28px;
            display: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .file-details.show {
            display: flex;
            align-items: center;
            animation: fadeInUp 0.4s ease forwards;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .file-icon {
            font-size: 2.2rem;
            color: var(--primary-color);
            margin-right: 18px;
        }

        .file-info {
            flex-grow: 1;
        }

        .file-name {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 350px;
        }

        .file-size {
            font-size: 0.85rem;
            color: var(--text-secondary);
        }

        .file-remove {
            color: var(--danger-color);
            cursor: pointer;
            font-size: 1.4rem;
            transition: all 0.2s ease;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }

        .file-remove:hover {
            background-color: rgba(248, 113, 113, 0.1);
            transform: rotate(90deg);
        }

        .progress-container {
            margin-bottom: 25px;
            display: none;
        }

        .progress {
            height: 12px;
            border-radius: 6px;
            background-color: #e2e8f0;
            margin-bottom: 10px;
            overflow: hidden;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .progress-bar {
            background: linear-gradient(to right, var(--primary-color), var(--accent-color));
            transition: width 0.3s ease;
            height: 100%;
            border-radius: 6px;
            position: relative;
            overflow: hidden;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                45deg,
                rgba(255, 255, 255, 0.2) 25%,
                transparent 25%,
                transparent 50%,
                rgba(255, 255, 255, 0.2) 50%,
                rgba(255, 255, 255, 0.2) 75%,
                transparent 75%,
                transparent
            );
            background-size: 30px 30px;
            animation: progress-animation 2s linear infinite;
            z-index: 1;
        }

        @keyframes progress-animation {
            0% {
                background-position: 0 0;
            }
            100% {
                background-position: 30px 0;
            }
        }

        .instructions-title {
            color: var(--text-primary);
            font-weight: 700;
            position: relative;
            padding-bottom: 12px;
            margin-bottom: 20px;
        }

        .instructions-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 4px;
            background: linear-gradient(to right, var(--accent-color), var(--primary-color));
            border-radius: 2px;
        }

        .instruction-item {
            background-color: transparent;
            border: none;
            border-left: 3px solid var(--accent-color);
            border-radius: 0 8px 8px 0;
            padding: 12px 18px;
            margin-bottom: 10px;
            font-size: 0.95rem;
            transition: all 0.2s ease;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
        }

        .instruction-item:hover {
            background-color: var(--primary-light);
            transform: translateX(5px);
        }

        .instruction-item i {
            color: var(--primary-color);
        }

        .feature-card {
            background-color: white;
            border-radius: 16px;
            padding: 20px 15px;
            text-align: center;
            transition: all 0.3s ease;
            height: 100%;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(67, 97, 238, 0.1);
        }

        .feature-icon {
            font-size: 2.2rem;
            color: var(--primary-color);
            margin-bottom: 15px;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.2);
            color: var(--secondary-color);
        }

        .feature-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .feature-text {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 12px;
            font-weight: 600;
            padding: 12px 28px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .btn-primary::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: all 0.6s ease;
            z-index: -1;
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(67, 97, 238, 0.3);
            background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
        }

        .btn-primary:hover::after {
            left: 100%;
        }

        .btn-primary:disabled {
            background: linear-gradient(135deg, #94a3b8, #cbd5e1);
            transform: none;
            box-shadow: none;
        }

        /* 用户操作按钮 - 移动到蓝色背景区域 */
        .user-actions {
            position: absolute;
            top: 20px;
            right: 30px;
            z-index: 10;
            display: flex;
            gap: 10px;
        }

        .user-btn {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            padding: 8px 16px;
            color: white;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            cursor: pointer;
        }

        .user-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

        .user-btn i {
            font-size: 1rem;
        }

        .user-info {
            background: rgba(74, 222, 128, 0.2);
            border: 1px solid rgba(74, 222, 128, 0.4);
            color: white;
        }

        .user-info:hover {
            background: rgba(74, 222, 128, 0.3);
            color: white;
        }

        /* 弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            z-index: 2000;
            display: none;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            max-width: 450px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            text-align: center;
            padding: 25px 30px 20px;
            position: relative;
            border-radius: 20px 20px 0 0;
        }

        .modal-close {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .modal-close:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .modal-icon {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0;
        }

        .modal-body {
            padding: 30px;
        }

        .form-floating {
            margin-bottom: 18px;
        }

        .form-control {
            border-radius: 12px;
            border: 1px solid var(--border-color);
            padding: 12px 16px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
        }

        .input-group {
            margin-bottom: 18px;
        }

        .btn-outline-secondary {
            border-color: var(--border-color);
            color: var(--text-secondary);
            background: #f8fafc;
            border-radius: 0 12px 12px 0;
            font-weight: 600;
            white-space: nowrap;
        }

        .btn-outline-secondary:hover {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary-modal {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 12px;
            font-weight: 600;
            padding: 12px 24px;
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            color: white;
            cursor: pointer;
        }

        .btn-primary-modal:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(67, 97, 238, 0.3);
        }

        .btn-primary-modal:disabled {
            background: linear-gradient(135deg, #94a3b8, #cbd5e1);
            transform: none;
            box-shadow: none;
        }

        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            border-radius: 12px;
            padding: 16px 20px;
            margin-bottom: 20px;
            border: none;
            font-size: 0.95rem;
        }

        .alert-danger {
            background-color: rgba(248, 113, 113, 0.15);
            border-left: 4px solid var(--danger-color);
            color: #b91c1c;
        }

        .alert-success {
            background-color: rgba(74, 222, 128, 0.15);
            border-left: 4px solid var(--success-color);
            color: #15803d;
        }

        .switch-form {
            text-align: center;
            padding: 20px 30px;
            background-color: #f8fafc;
            border-top: 1px solid #e2e8f0;
            border-radius: 0 0 20px 20px;
        }

        .switch-form a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .switch-form a:hover {
            color: var(--secondary-color);
            text-decoration: underline;
        }

        .countdown {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .alert {
            border-radius: 12px;
            padding: 16px 22px;
            margin-bottom: 25px;
            display: none;
            animation: fadeInUp 0.4s ease forwards;
            position: relative;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        }

        .alert-danger {
            background-color: rgba(248, 113, 113, 0.15);
            border-left: 4px solid var(--danger-color);
            color: #b91c1c;
        }

        .alert-success {
            background-color: rgba(74, 222, 128, 0.15);
            border-left: 4px solid var(--success-color);
            color: #15803d;
        }

        .alert-info {
            background-color: rgba(67, 97, 238, 0.15);
            border-left: 4px solid var(--primary-color);
            color: #1e40af;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .card-body {
                padding: 25px 20px;
            }
            
            .app-title {
                font-size: 1.8rem;
            }
            
            .upload-zone {
                padding: 25px 15px;
            }
        }
    </style>
</head>
<body>
    <!-- 用户操作按钮 - 移动到蓝色背景区域 -->
    

    <!-- 背景装饰 -->
    <div class="bg-decoration bg-decoration-1"></div>
    <div class="bg-decoration bg-decoration-2"></div>
    <div class="bg-decoration bg-decoration-3"></div>

    <div class="container">
        <div class="app-card animate__animated animate__fadeInUp">
            <div class="card-header-bg">
                <div class="user-actions">
                    {% if session.get('user_id') %} 
                        <span class="user-btn user-info">
                            <i class="bi bi-person-circle"></i>
                            {{ session.get('username', '用户') }}
                        </span>
                        <a href="/logout" class="user-btn">
                            <i class="bi bi-box-arrow-right"></i>
                            退出
                        </a>
                    {% else %}
                        <button class="user-btn" onclick="showLoginModal()">
                            <i class="bi bi-box-arrow-in-right"></i>
                            登录
                        </button>
                        <button class="user-btn" onclick="showRegisterModal()">
                            <i class="bi bi-person-plus"></i>
                            注册
                        </button>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-7">
                        <h1 class="app-title">账单分析系统</h1>
                        <p class="app-subtitle">上传您的CSV账单文件，获取专业的收支分析</p>
                        
                        <!-- 上传区域 -->
                        <div class="upload-zone" id="uploadZone">
                            <div class="upload-icon">
                                <i class="bi bi-cloud-arrow-up-fill"></i>
                            </div>
                            <h5 class="upload-text">点击或拖拽文件到此处上传</h5>
                            <p class="file-types">支持格式: CSV</p>
                            <input type="file" class="file-input" id="fileInput" accept=".csv">
                        </div>
                        
                        <!-- 文件详情 -->
                        <div class="file-details" id="fileDetails">
                            <div class="file-icon">
                                <i class="bi bi-file-earmark-spreadsheet-fill"></i>
                            </div>
                            <div class="file-info">
                                <div class="file-name" id="fileName">文件名.csv</div>
                                <div class="file-size" id="fileSize">0 KB</div>
                            </div>
                            <div class="file-remove" id="fileRemove">
                                <i class="bi bi-x-lg"></i>
                            </div>
                        </div>
                        
                        <!-- 进度条 -->
                        <div class="progress-container" id="progressContainer">
                            <div class="progress">
                                <div class="progress-bar" id="progressBar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                            <small class="text-muted" id="progressText">上传中... 0%</small>
                        </div>
                        
                        <!-- 提示信息 -->
                        <div class="alert alert-danger" id="errorAlert" role="alert"></div>
                        <div class="alert alert-success" id="successAlert" role="alert"></div>
                        
                        <!-- 上传按钮 -->
                        <button type="button" class="btn btn-primary btn-lg w-100" id="uploadBtn" disabled>
                            <i class="bi bi-lightning-charge-fill me-2"></i>开始分析
                        </button>
                    </div>
                    
                    <div class="col-lg-5 mt-4 mt-lg-0">
                        <h5 class="instructions-title">使用说明</h5>
                        <ul class="list-group mb-4">
                            <li class="list-group-item instruction-item">
                                <i class="bi bi-1-circle-fill me-2"></i>选择或拖拽您的CSV账单文件
                            </li>
                            <li class="list-group-item instruction-item">
                                <i class="bi bi-2-circle-fill me-2"></i>系统将自动从第11行开始读取数据
                            </li>
                            <li class="list-group-item instruction-item">
                                <i class="bi bi-3-circle-fill me-2"></i>点击"开始分析"按钮上传文件
                            </li>
                            <li class="list-group-item instruction-item">
                                <i class="bi bi-4-circle-fill me-2"></i>等待分析完成，系统会自动展示结果
                            </li>
                        </ul>
                        
                        <div class="row g-3">
                            <div class="col-6">
                                <div class="feature-card">
                                    <div class="feature-icon">
                                        <i class="bi bi-graph-up-arrow"></i>
                                    </div>
                                    <h6 class="feature-title">数据可视化</h6>
                                    <p class="feature-text">直观图表展示收支情况</p>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="feature-card">
                                    <div class="feature-icon">
                                        <i class="bi bi-bar-chart-fill"></i>
                                    </div>
                                    <h6 class="feature-title">排行榜分析</h6>
                                    <p class="feature-text">了解主要支出和收入来源</p>
                                </div>
                            </div>
                            <div class="col-6 mt-3">
                                <div class="feature-card">
                                    <div class="feature-icon">
                                        <i class="bi bi-calendar-check-fill"></i>
                                    </div>
                                    <h6 class="feature-title">时间趋势</h6>
                                    <p class="feature-text">掌握收支时间规律</p>
                                </div>
                            </div>
                            <div class="col-6 mt-3">
                                <div class="feature-card">
                                    <div class="feature-icon">
                                        <i class="bi bi-search"></i>
                                    </div>
                                    <h6 class="feature-title">明细查询</h6>
                                    <p class="feature-text">快速搜索账单记录</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 登录弹窗 -->
    <div class="modal-overlay" id="loginModal">
        <div class="modal-content">
            <div class="modal-header">
                <button class="modal-close" onclick="closeModal('loginModal')">
                    <i class="bi bi-x"></i>
                </button>
                <div class="modal-icon">
                    <i class="bi bi-person-circle"></i>
                </div>
                <h2 class="modal-title">用户登录</h2>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger" id="loginErrorAlert" style="display: none;"></div>
                <div class="alert alert-success" id="loginSuccessAlert" style="display: none;"></div>

                <form id="loginForm">
                    <div class="form-floating">
                        <input type="email" class="form-control" id="loginEmail" placeholder="邮箱地址" required>
                        <label for="loginEmail">
                            <i class="bi bi-envelope me-2"></i>邮箱地址
                        </label>
                    </div>

                    <div class="form-floating">
                        <input type="password" class="form-control" id="loginPassword" placeholder="密码" required>
                        <label for="loginPassword">
                            <i class="bi bi-lock me-2"></i>密码
                        </label>
                    </div>

                    <button type="submit" class="btn-primary-modal" id="loginBtn">
                        <span class="btn-text">
                            <i class="bi bi-box-arrow-in-right me-2"></i>登录
                        </span>
                        <div class="loading-spinner"></div>
                    </button>
                </form>
            </div>
            <div class="switch-form">
                <p class="mb-0">
                    还没有账户？
                    <a href="#" onclick="switchToRegister()">立即注册</a>
                </p>
            </div>
        </div>
    </div>

    <!-- 注册弹窗 -->
    <div class="modal-overlay" id="registerModal">
        <div class="modal-content">
            <div class="modal-header">
                <button class="modal-close" onclick="closeModal('registerModal')">
                    <i class="bi bi-x"></i>
                </button>
                <div class="modal-icon">
                    <i class="bi bi-person-plus"></i>
                </div>
                <h2 class="modal-title">用户注册</h2>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger" id="registerErrorAlert" style="display: none;"></div>
                <div class="alert alert-success" id="registerSuccessAlert" style="display: none;"></div>

                <form id="registerForm">
                    <div class="form-floating">
                        <input type="text" class="form-control" id="registerUsername" placeholder="用户名" required>
                        <label for="registerUsername">
                            <i class="bi bi-person me-2"></i>用户名
                        </label>
                    </div>

                    <div class="form-floating">
                        <input type="email" class="form-control" id="registerEmail" placeholder="邮箱地址" required>
                        <label for="registerEmail">
                            <i class="bi bi-envelope me-2"></i>邮箱地址
                        </label>
                    </div>

                    <div class="input-group">
                        <div class="form-floating flex-grow-1">
                            <input type="text" class="form-control" id="registerVerificationCode" placeholder="验证码" required>
                            <label for="registerVerificationCode">
                                <i class="bi bi-shield-check me-2"></i>验证码
                            </label>
                        </div>
                        <button type="button" class="btn btn-outline-secondary" id="sendCodeBtn">
                            获取验证码
                        </button>
                    </div>

                    <div class="form-floating">
                        <input type="password" class="form-control" id="registerPassword" placeholder="密码" required>
                        <label for="registerPassword">
                            <i class="bi bi-lock me-2"></i>密码
                        </label>
                    </div>

                    <div class="form-floating">
                        <input type="password" class="form-control" id="registerConfirmPassword" placeholder="确认密码" required>
                        <label for="registerConfirmPassword">
                            <i class="bi bi-lock-fill me-2"></i>确认密码
                        </label>
                    </div>

                    <button type="submit" class="btn-primary-modal" id="registerBtn">
                        <span class="btn-text">
                            <i class="bi bi-person-plus me-2"></i>注册账户
                        </span>
                        <div class="loading-spinner"></div>
                    </button>
                </form>
            </div>
            <div class="switch-form">
                <p class="mb-0">
                    已有账户？
                    <a href="#" onclick="switchToLogin()">立即登录</a>
                </p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 弹窗管理函数
        function showLoginModal() {
            document.getElementById('loginModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        function showRegisterModal() {
            document.getElementById('registerModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            document.body.style.overflow = 'auto';
            // 清空表单
            if (modalId === 'loginModal') {
                document.getElementById('loginForm').reset();
                hideModalAlerts('login');
            } else if (modalId === 'registerModal') {
                document.getElementById('registerForm').reset();
                hideModalAlerts('register');
            }
        }

        function switchToRegister() {
            closeModal('loginModal');
            showRegisterModal();
        }

        function switchToLogin() {
            closeModal('registerModal');
            showLoginModal();
        }

        // 点击背景关闭弹窗
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal-overlay')) {
                closeModal(e.target.id);
            }
        });

        // ESC键关闭弹窗
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const loginModal = document.getElementById('loginModal');
                const registerModal = document.getElementById('registerModal');
                if (loginModal.style.display === 'flex') {
                    closeModal('loginModal');
                } else if (registerModal.style.display === 'flex') {
                    closeModal('registerModal');
                }
            }
        });

        // 弹窗提示函数
        function showModalError(type, message) {
            hideModalAlerts(type);
            const alertId = type + 'ErrorAlert';
            const alertElement = document.getElementById(alertId);
            alertElement.textContent = message;
            alertElement.style.display = 'block';
        }

        function showModalSuccess(type, message) {
            hideModalAlerts(type);
            const alertId = type + 'SuccessAlert';
            const alertElement = document.getElementById(alertId);
            alertElement.textContent = message;
            alertElement.style.display = 'block';
        }

        function hideModalAlerts(type) {
            document.getElementById(type + 'ErrorAlert').style.display = 'none';
            document.getElementById(type + 'SuccessAlert').style.display = 'none';
        }

        function setModalLoading(type, loading) {
            const btnId = type + 'Btn';
            const btn = document.getElementById(btnId);
            const spinner = btn.querySelector('.loading-spinner');
            const text = btn.querySelector('.btn-text');
            
            btn.disabled = loading;
            if (loading) {
                text.style.display = 'none';
                spinner.style.display = 'inline-block';
            } else {
                text.style.display = 'inline-block';
                spinner.style.display = 'none';
            }
        }

        // 登录表单处理
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('loginEmail').value.trim();
            const password = document.getElementById('loginPassword').value;

            if (!email || !password) {
                showModalError('login', '请填写所有字段');
                return;
            }

            setModalLoading('login', true);
            hideModalAlerts('login');

            fetch('/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    email: email,
                    password: password
                })
            })
            .then(response => response.json())
            .then(data => {
                setModalLoading('login', false);
                
                if (data.error) {
                    showModalError('login', data.error);
                } else {
                    showModalSuccess('login', data.message);
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                }
            })
            .catch(error => {
                setModalLoading('login', false);
                showModalError('login', '登录失败，请重试');
                console.error('登录错误:', error);
            });
        });

        // 注册表单相关变量
        let countdown = 0;
        let countdownInterval = null;

        // 发送验证码
        document.getElementById('sendCodeBtn').addEventListener('click', function() {
            const email = document.getElementById('registerEmail').value.trim();

            if (!email) {
                showModalError('register', '请先输入邮箱地址');
                return;
            }

            if (!isValidEmail(email)) {
                showModalError('register', '请输入有效的邮箱地址');
                return;
            }

            const sendCodeBtn = document.getElementById('sendCodeBtn');
            sendCodeBtn.disabled = true;
            const originalText = sendCodeBtn.textContent;
            sendCodeBtn.textContent = '发送中...';

            fetch('/send_verification_code', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email: email })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showModalError('register', data.error);
                    sendCodeBtn.disabled = false;
                    sendCodeBtn.textContent = originalText;
                } else {
                    showModalSuccess('register', data.message);
                    startCountdown();
                }
            })
            .catch(error => {
                showModalError('register', '发送验证码失败，请重试');
                sendCodeBtn.disabled = false;
                sendCodeBtn.textContent = originalText;
                console.error('发送验证码错误:', error);
            });
        });

        // 注册表单提交
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('registerUsername').value.trim();
            const email = document.getElementById('registerEmail').value.trim();
            const verificationCode = document.getElementById('registerVerificationCode').value.trim();
            const password = document.getElementById('registerPassword').value;
            const confirmPassword = document.getElementById('registerConfirmPassword').value;

            if (!username || !email || !verificationCode || !password || !confirmPassword) {
                showModalError('register', '请填写所有字段');
                return;
            }

            if (!isValidEmail(email)) {
                showModalError('register', '请输入有效的邮箱地址');
                return;
            }

            if (password.length < 6) {
                showModalError('register', '密码长度至少为6位');
                return;
            }

            if (password !== confirmPassword) {
                showModalError('register', '两次输入的密码不一致');
                return;
            }

            setModalLoading('register', true);
            hideModalAlerts('register');

            fetch('/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: username,
                    email: email,
                    verification_code: verificationCode,
                    password: password
                })
            })
            .then(response => response.json())
            .then(data => {
                setModalLoading('register', false);
                
                if (data.error) {
                    showModalError('register', data.error);
                } else {
                    showModalSuccess('register', data.message + '，正在切换到登录...');
                    setTimeout(() => {
                        switchToLogin();
                    }, 2000);
                }
            })
            .catch(error => {
                setModalLoading('register', false);
                showModalError('register', '注册失败，请重试');
                console.error('注册错误:', error);
            });
        });

        // 验证码倒计时
        function startCountdown() {
            countdown = 60;
            updateCountdownButton();
            
            countdownInterval = setInterval(() => {
                countdown--;
                if (countdown <= 0) {
                    clearInterval(countdownInterval);
                    const sendCodeBtn = document.getElementById('sendCodeBtn');
                    sendCodeBtn.disabled = false;
                    sendCodeBtn.textContent = '获取验证码';
                    sendCodeBtn.classList.remove('countdown');
                } else {
                    updateCountdownButton();
                }
            }, 1000);
        }

        function updateCountdownButton() {
            const sendCodeBtn = document.getElementById('sendCodeBtn');
            sendCodeBtn.textContent = `${countdown}秒后重试`;
            sendCodeBtn.classList.add('countdown');
        }

        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        // 原有的文件上传功能
        document.addEventListener('DOMContentLoaded', function() {
            const fileInput = document.getElementById('fileInput');
            const uploadBtn = document.getElementById('uploadBtn');
            const fileDetails = document.getElementById('fileDetails');
            const fileName = document.getElementById('fileName');
            const fileSize = document.getElementById('fileSize');
            const fileRemove = document.getElementById('fileRemove');
            const uploadZone = document.getElementById('uploadZone');
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            const errorAlert = document.getElementById('errorAlert');
            const successAlert = document.getElementById('successAlert');

            // 格式化文件大小
            function formatFileSize(bytes) {
                if (bytes < 1024) return bytes + ' B';
                else if (bytes < 1048576) return (bytes / 1024).toFixed(2) + ' KB';
                else return (bytes / 1048576).toFixed(2) + ' MB';
            }

            // 文件选择处理
            fileInput.addEventListener('change', function() {
                handleFileSelection(this.files);
            });

            // 拖放功能
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                uploadZone.addEventListener(eventName, preventDefaults, false);
            });

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            ['dragenter', 'dragover'].forEach(eventName => {
                uploadZone.addEventListener(eventName, function() {
                    uploadZone.classList.add('drag-over');
                }, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                uploadZone.addEventListener(eventName, function() {
                    uploadZone.classList.remove('drag-over');
                }, false);
            });

            uploadZone.addEventListener('drop', function(e) {
                const dt = e.dataTransfer;
                const files = dt.files;
                handleFileSelection(files);
            }, false);

            // 处理文件选择
            function handleFileSelection(files) {
                if (files.length > 0) {
                    const file = files[0];
                    
                    if (file.name.toLowerCase().endsWith('.csv')) {
                        uploadBtn.disabled = false;
                        fileName.textContent = file.name;
                        fileSize.textContent = formatFileSize(file.size);
                        fileDetails.classList.add('show');
                        
                        // 添加动画效果
                        uploadZone.classList.add('animate__animated', 'animate__pulse');
                        setTimeout(() => {
                            uploadZone.classList.remove('animate__animated', 'animate__pulse');
                        }, 800);
                    } else {
                        showError('请选择CSV格式的文件');
                        resetFileInput();
                    }
                }
            }

            // 移除文件
            fileRemove.addEventListener('click', function() {
                resetFileInput();
            });

            // 重置文件输入
            function resetFileInput() {
                fileInput.value = '';
                fileDetails.classList.remove('show');
                uploadBtn.disabled = true;
            }

            // 点击上传按钮
            uploadBtn.addEventListener('click', function() {
                if (!fileInput.files[0]) {
                    showError('请先选择文件');
                    return;
                }

                const file = fileInput.files[0];
                const formData = new FormData();
                formData.append('file', file);

                // 重置并显示进度条
                hideAlerts();
                progressContainer.style.display = 'block';
                updateProgress(0);
                uploadBtn.disabled = true;

                // 模拟上传进度
                let progress = 0;
                const interval = setInterval(() => {
                    progress += 5;
                    if (progress <= 90) {
                        updateProgress(progress);
                    } else {
                        clearInterval(interval);
                    }
                }, 100);

                // 发送上传请求
                fetch('/upload', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    clearInterval(interval);
                    
                    if (response.status === 401) {
                        return response.json().then(data => {
                            if (data.need_login) {
                                // 需要登录
                                progressContainer.style.display = 'none';
                                uploadBtn.disabled = false;
                                showLoginPrompt();
                                return;
                            }
                            throw new Error(data.error || '未授权');
                        });
                    }
                    
                    updateProgress(100);
                    
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.error || '上传失败');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data) {
                        showSuccess(data.message || '上传成功，正在分析数据...');
                        setTimeout(() => {
                            window.location.href = data.redirect;
                        }, 1500);
                    }
                })
                .catch(error => {
                    showError(error.message);
                    progressContainer.style.display = 'none';
                    uploadBtn.disabled = false;
                });
            });

            // 显示登录提示
            function showLoginPrompt() {
                const loginPrompt = document.createElement('div');
                loginPrompt.className = 'alert alert-info';
                loginPrompt.style.display = 'block';
                loginPrompt.innerHTML = `
                    <i class="bi bi-info-circle-fill me-2"></i>
                    <strong>需要登录</strong><br>
                    请先登录后再上传文件。如果您还没有账户，请先注册。
                    <div class="mt-3">
                        <button class="btn btn-primary btn-sm me-2" onclick="showLoginModal()">
                            <i class="bi bi-box-arrow-in-right me-1"></i>立即登录
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="showRegisterModal()">
                            <i class="bi bi-person-plus me-1"></i>注册账户
                        </button>
                    </div>
                `;
                
                // 插入到错误提示之前
                errorAlert.parentNode.insertBefore(loginPrompt, errorAlert);
                
                // 8秒后自动移除
                setTimeout(() => {
                    if (loginPrompt.parentNode) {
                        loginPrompt.parentNode.removeChild(loginPrompt);
                    }
                }, 8000);
            }

            // 更新进度条
            function updateProgress(value) {
                progressBar.style.width = value + '%';
                progressBar.setAttribute('aria-valuenow', value);
                progressText.textContent = `上传中... ${value}%`;
            }

            // 显示错误信息
            function showError(message) {
                hideAlerts();
                errorAlert.textContent = message;
                errorAlert.style.display = 'block';
            }

            // 显示成功信息
            function showSuccess(message) {
                hideAlerts();
                successAlert.textContent = message;
                successAlert.style.display = 'block';
            }

            // 隐藏所有提示
            function hideAlerts() {
                errorAlert.style.display = 'none';
                successAlert.style.display = 'none';
            }
        });
    </script>
</body>
</html> 