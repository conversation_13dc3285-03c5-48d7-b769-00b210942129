# 账单分析系统

一个基于Web的账单分析工具，可以上传CSV格式的账单文件，系统将自动分析账单数据并生成可视化图表和统计信息。

## 功能特点

- 上传CSV格式账单文件进行分析
- 自动识别账单文件编码
- 统计收入、支出和结余
- 按分类、收支类型进行数据分析
- 生成饼图展示支出分类占比
- 显示收支趋势图表
- 提供明细数据搜索和过滤功能
- 响应式设计，适配各种设备

## 技术栈

- 前端：HTML, CSS, JavaScript, Bootstrap 5
- 数据可视化：ECharts, Chart.js
- 后端：Flask (Python)
- 数据处理：Pandas

## 安装使用

### 环境要求

- Python 3.6+
- pip 包管理工具

### 安装依赖

```bash
pip install flask pandas chardet
```

### 运行系统

1. 克隆或下载本仓库
2. 进入项目根目录
3. 运行Flask应用

```bash
python app.py
```

4. 在浏览器中访问 `http://localhost:5000` 

### 使用方法

1. 在首页点击"选择CSV文件"按钮，上传账单CSV文件
2. 上传成功后系统会自动处理数据并跳转到分析页面
3. 分析页面显示账单汇总信息和可视化图表
4. 可以切换到"账单明细"标签查看详细数据
5. 使用搜索和过滤功能查找特定记录

## 数据格式要求

系统将从CSV文件的第11行开始读取数据，要求CSV文件包含以下列（或自动识别）：

1. 记录时间
2. 分类
3. 收支类型
4. 金额
5. 备注
6. 账户
7. 来源
8. 标签

## 开发进度

- 2025-07-27: 项目初始化
- 2025-07-27: 实现文件上传功能
- 2025-07-27: 实现数据分析和可视化功能
- 2025-07-27: 完成账单明细页面

## 注意事项

- 上传文件大小限制为16MB
- 支持多种编码格式的CSV文件
- 账单数据会临时保存在服务器上，刷新页面后需要重新上传 