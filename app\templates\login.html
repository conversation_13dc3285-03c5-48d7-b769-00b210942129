<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - 账单分析系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        :root {
            --primary-color: #4361ee;
            --primary-light: rgba(67, 97, 238, 0.1);
            --secondary-color: #3f37c9;
            --accent-color: #4cc9f0;
            --success-color: #4ade80;
            --danger-color: #f87171;
            --bg-color: #f8fafc;
            --card-bg: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #cbd5e1;
        }

        body {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .bg-decoration {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            z-index: -1;
        }

        .bg-decoration-1 {
            top: -100px;
            left: -100px;
            width: 300px;
            height: 300px;
        }

        .bg-decoration-2 {
            bottom: -150px;
            right: -150px;
            width: 400px;
            height: 400px;
        }

        .login-container {
            max-width: 420px;
            width: 100%;
            padding: 20px;
        }

        .login-card {
            background: var(--card-bg);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            position: relative;
        }

        .login-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            text-align: center;
            padding: 40px 30px 30px;
            position: relative;
        }

        .login-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            opacity: 0.2;
        }

        .login-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
        }

        .login-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .login-subtitle {
            opacity: 0.9;
            font-size: 1rem;
            position: relative;
            z-index: 1;
        }

        .login-body {
            padding: 35px 30px;
        }

        .form-floating {
            margin-bottom: 20px;
        }

        .form-control {
            border-radius: 12px;
            border: 1px solid var(--border-color);
            padding: 12px 16px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
        }

        .form-floating > label {
            color: var(--text-secondary);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 12px;
            font-weight: 600;
            padding: 12px 24px;
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(67, 97, 238, 0.3);
        }

        .btn-primary:disabled {
            background: linear-gradient(135deg, #94a3b8, #cbd5e1);
            transform: none;
            box-shadow: none;
        }

        .alert {
            border-radius: 12px;
            padding: 16px 20px;
            margin-bottom: 20px;
            border: none;
            font-size: 0.95rem;
        }

        .alert-danger {
            background-color: rgba(248, 113, 113, 0.15);
            border-left: 4px solid var(--danger-color);
            color: #b91c1c;
        }

        .alert-success {
            background-color: rgba(74, 222, 128, 0.15);
            border-left: 4px solid var(--success-color);
            color: #15803d;
        }

        .login-footer {
            padding: 20px 30px;
            background-color: #f8fafc;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }

        .login-footer a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .login-footer a:hover {
            color: var(--secondary-color);
            text-decoration: underline;
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            text-decoration: none;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .back-btn:hover {
            background: white;
            color: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 480px) {
            .login-container {
                padding: 15px;
            }
            
            .login-body {
                padding: 25px 20px;
            }
            
            .login-header {
                padding: 30px 20px 25px;
            }
        }
    </style>
</head>
<body>
    <!-- 背景装饰 -->
    <div class="bg-decoration bg-decoration-1"></div>
    <div class="bg-decoration bg-decoration-2"></div>

    <!-- 返回按钮 -->
    <a href="/" class="back-btn" title="返回首页">
        <i class="bi bi-arrow-left"></i>
    </a>

    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="login-icon">
                    <i class="bi bi-person-circle"></i>
                </div>
                <h1 class="login-title">用户登录</h1>
                <p class="login-subtitle">登录您的账户以继续使用</p>
            </div>
            
            <div class="login-body">
                <div class="alert alert-danger" id="errorAlert" style="display: none;"></div>
                <div class="alert alert-success" id="successAlert" style="display: none;"></div>

                <form id="loginForm">
                    <div class="form-floating">
                        <input type="email" class="form-control" id="email" placeholder="邮箱地址" required>
                        <label for="email">
                            <i class="bi bi-envelope me-2"></i>邮箱地址
                        </label>
                    </div>

                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" placeholder="密码" required>
                        <label for="password">
                            <i class="bi bi-lock me-2"></i>密码
                        </label>
                    </div>

                    <button type="submit" class="btn btn-primary" id="loginBtn">
                        <span class="btn-text">
                            <i class="bi bi-box-arrow-in-right me-2"></i>登录
                        </span>
                        <div class="loading-spinner"></div>
                    </button>
                </form>
            </div>

            <div class="login-footer">
                <p class="mb-0">
                    还没有账户？
                    <a href="/register">立即注册</a>
                </p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');
            const loginBtn = document.getElementById('loginBtn');
            const errorAlert = document.getElementById('errorAlert');
            const successAlert = document.getElementById('successAlert');
            const loadingSpinner = loginBtn.querySelector('.loading-spinner');
            const btnText = loginBtn.querySelector('.btn-text');

            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const email = document.getElementById('email').value.trim();
                const password = document.getElementById('password').value;

                if (!email || !password) {
                    showError('请填写所有字段');
                    return;
                }

                // 显示加载状态
                setLoading(true);
                hideAlerts();

                // 发送登录请求
                fetch('/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: email,
                        password: password
                    })
                })
                .then(response => response.json())
                .then(data => {
                    setLoading(false);
                    
                    if (data.error) {
                        showError(data.error);
                    } else {
                        showSuccess(data.message);
                        setTimeout(() => {
                            window.location.href = data.redirect;
                        }, 1000);
                    }
                })
                .catch(error => {
                    setLoading(false);
                    showError('登录失败，请重试');
                    console.error('登录错误:', error);
                });
            });

            function setLoading(loading) {
                loginBtn.disabled = loading;
                if (loading) {
                    btnText.style.display = 'none';
                    loadingSpinner.style.display = 'inline-block';
                } else {
                    btnText.style.display = 'inline-block';
                    loadingSpinner.style.display = 'none';
                }
            }

            function showError(message) {
                hideAlerts();
                errorAlert.textContent = message;
                errorAlert.style.display = 'block';
            }

            function showSuccess(message) {
                hideAlerts();
                successAlert.textContent = message;
                successAlert.style.display = 'block';
            }

            function hideAlerts() {
                errorAlert.style.display = 'none';
                successAlert.style.display = 'none';
            }
        });
    </script>
</body>
</html>