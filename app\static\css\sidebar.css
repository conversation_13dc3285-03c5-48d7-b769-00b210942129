/* 左侧导航栏样式 */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: 220px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    overflow-y: auto;
    transition: transform 0.3s ease;
}

.sidebar.collapsed {
    transform: translateX(-220px);
}

.sidebar-header {
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.sidebar-title {
    color: white;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    text-align: center;
}

.sidebar-nav {
    padding: 20px 0;
}

.nav-section {
    margin-bottom: 10px;
}

.nav-item {
    margin: 0;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    font-size: 14px;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(5px);
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-right: 3px solid #fff;
}

.nav-link i {
    margin-right: 10px;
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.nav-submenu {
    background: rgba(0, 0, 0, 0.1);
    display: none;
}

.nav-submenu.show {
    display: block;
}

.nav-submenu .nav-link {
    padding-left: 50px;
    font-size: 13px;
    color: rgba(255, 255, 255, 0.7);
}

.nav-submenu .nav-link:hover {
    color: rgba(255, 255, 255, 0.9);
}

.nav-submenu .nav-link.active {
    color: white;
    background: rgba(255, 255, 255, 0.15);
}

/* 主内容区域调整 */
.main-content {
    margin-left: 220px;
    transition: margin-left 0.3s ease;
    min-height: 100vh;
}

.main-content.expanded {
    margin-left: 0;
}

/* 切换按钮 */
.sidebar-toggle {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1001;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.sidebar-toggle:hover {
    background: #5a6fd8;
    transform: scale(1.1);
}

.sidebar-toggle.moved {
    left: 300px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .sidebar-toggle.moved {
        left: 20px;
    }
}

/* 内容区域样式调整 */
.content-section {
    display: none;
    animation: fadeIn 0.3s ease;
}

.content-section.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 滚动条样式 */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* 导航栏展开/收起图标 */
.nav-toggle-icon {
    transition: transform 0.3s ease;
}

.nav-toggle-icon.rotated {
    transform: rotate(90deg);
}

/* 面包屑导航 */
.breadcrumb-nav {
    background: rgba(255, 255, 255, 0.9);
    padding: 15px 30px;
    margin-bottom: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.breadcrumb-nav .breadcrumb {
    margin: 0;
    background: none;
    padding: 0;
}

.breadcrumb-nav .breadcrumb-item {
    color: #666;
}

.breadcrumb-nav .breadcrumb-item.active {
    color: #333;
    font-weight: 600;
}

/* 页面标题样式 */
.page-title {
    color: #333;
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 30px;
    text-align: center;
}

/* 卡片容器调整 */
.content-section .card {
    margin-bottom: 25px;
}

/* 加载动画 */
.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
